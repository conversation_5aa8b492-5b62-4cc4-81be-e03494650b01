#!/usr/bin/env python3
"""
Enhanced ROI Calculator for Solar AI Cleaning & Monitoring System
Comprehensive financial analysis and return on investment calculations
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
from enum import Enum
import numpy as np
import logging

logger = logging.getLogger(__name__)

# Enhanced ROI Models
class CleaningMethodROI(str, Enum):
    DRONE_WATER = "drone_water"
    DRONE_WATERLESS = "drone_waterless"
    CRAWLER_ROBOTS = "crawler_robots"
    NANO_COATINGS = "nano_coatings"
    AIR_BLOWERS = "air_blowers"
    ULTRASONIC = "ultrasonic_vibrations"
    ELECTROSTATIC = "electrostatic_cleaning"
    UV_SURFACE = "uv_surface_cleaning"
    PREDICTIVE_MAINTENANCE = "predictive_maintenance"

class SystemConfiguration(BaseModel):
    system_size_mw: float = Field(..., gt=0, description="System size in megawatts")
    location: str = Field(..., description="Installation location")
    installation_year: int = Field(..., description="Year of installation")
    panel_type: str = Field(default="monocrystalline", description="Type of solar panels")
    inverter_efficiency: float = Field(default=0.96, ge=0.8, le=1.0, description="Inverter efficiency")
    system_degradation_rate: float = Field(default=0.005, description="Annual degradation rate")

class EnvironmentalFactors(BaseModel):
    average_irradiance: float = Field(..., gt=0, description="Average solar irradiance kWh/m²/day")
    dust_accumulation_rate: float = Field(..., ge=0, le=100, description="Daily dust accumulation %")
    rainfall_days_per_year: int = Field(default=20, ge=0, le=365, description="Natural cleaning days")
    wind_speed_average: float = Field(default=5.0, ge=0, description="Average wind speed m/s")
    humidity_average: float = Field(default=40.0, ge=0, le=100, description="Average humidity %")

class FinancialParameters(BaseModel):
    electricity_price_per_kwh: float = Field(..., gt=0, description="Electricity price $/kWh")
    electricity_price_escalation: float = Field(default=0.03, description="Annual price increase rate")
    discount_rate: float = Field(default=0.08, description="Discount rate for NPV calculation")
    tax_rate: float = Field(default=0.25, description="Corporate tax rate")
    depreciation_years: int = Field(default=10, description="Equipment depreciation period")

class CleaningCosts(BaseModel):
    method: CleaningMethodROI
    cost_per_mw: float = Field(..., gt=0, description="Cost per MW per cleaning")
    frequency_per_year: int = Field(..., gt=0, description="Cleaning frequency per year")
    water_cost_per_liter: float = Field(default=0.001, description="Water cost $/liter")
    labor_cost_per_hour: float = Field(default=25.0, description="Labor cost $/hour")
    equipment_depreciation: float = Field(default=0.1, description="Annual equipment depreciation")

class ROIAnalysisRequest(BaseModel):
    system_config: SystemConfiguration
    environmental_factors: EnvironmentalFactors
    financial_params: FinancialParameters
    cleaning_costs: CleaningCosts
    analysis_years: int = Field(default=25, ge=1, le=50, description="Analysis period in years")

class MonthlyROIData(BaseModel):
    month: int
    year: int
    energy_production_kwh: float
    efficiency_loss_percent: float
    energy_loss_kwh: float
    revenue_loss: float
    cleaning_cost: float
    net_savings: float
    cumulative_savings: float

class YearlyROIData(BaseModel):
    year: int
    total_energy_production_kwh: float
    total_efficiency_loss_percent: float
    total_energy_loss_kwh: float
    total_revenue_loss: float
    total_cleaning_cost: float
    net_annual_savings: float
    cumulative_savings: float
    roi_percent: float
    payback_achieved: bool

class ComparisonAnalysis(BaseModel):
    method: CleaningMethodROI
    total_cost_5_years: float
    total_savings_5_years: float
    net_benefit_5_years: float
    roi_5_years: float
    payback_period_months: float
    efficiency_improvement: float
    water_usage_liters: float
    carbon_footprint_kg: float

class DetailedROICalculation(BaseModel):
    request_id: str
    calculation_date: datetime
    system_config: SystemConfiguration
    environmental_factors: EnvironmentalFactors
    financial_params: FinancialParameters
    cleaning_costs: CleaningCosts
    
    # Summary Results
    total_investment: float
    total_savings: float
    net_present_value: float
    internal_rate_of_return: float
    payback_period_months: float
    roi_5_years: float
    roi_10_years: float
    roi_25_years: float
    
    # Detailed Analysis
    monthly_data: List[MonthlyROIData]
    yearly_data: List[YearlyROIData]
    
    # Environmental Impact
    water_saved_liters: float
    carbon_reduced_kg: float
    energy_efficiency_gain: float
    
    # Risk Analysis
    best_case_roi: float
    worst_case_roi: float
    sensitivity_analysis: Dict[str, float]

# Enhanced ROI Calculator Class
class EnhancedROICalculator:
    """Comprehensive ROI calculator for solar cleaning systems"""
    
    def __init__(self):
        self.cleaning_method_specs = {
            CleaningMethodROI.DRONE_WATER: {
                "efficiency": 0.85, "water_usage": 0.7, "duration_hours": 0.75,
                "equipment_cost": 50000, "maintenance_factor": 1.2
            },
            CleaningMethodROI.DRONE_WATERLESS: {
                "efficiency": 0.90, "water_usage": 0.0, "duration_hours": 0.5,
                "equipment_cost": 75000, "maintenance_factor": 1.0
            },
            CleaningMethodROI.CRAWLER_ROBOTS: {
                "efficiency": 0.95, "water_usage": 0.1, "duration_hours": 1.0,
                "equipment_cost": 100000, "maintenance_factor": 0.8
            },
            CleaningMethodROI.NANO_COATINGS: {
                "efficiency": 0.75, "water_usage": 0.0, "duration_hours": 0.0,
                "equipment_cost": 200000, "maintenance_factor": 0.1
            },
            CleaningMethodROI.AIR_BLOWERS: {
                "efficiency": 0.70, "water_usage": 0.0, "duration_hours": 0.33,
                "equipment_cost": 30000, "maintenance_factor": 1.1
            },
            CleaningMethodROI.ULTRASONIC: {
                "efficiency": 0.80, "water_usage": 0.0, "duration_hours": 0.25,
                "equipment_cost": 60000, "maintenance_factor": 0.9
            },
            CleaningMethodROI.ELECTROSTATIC: {
                "efficiency": 0.85, "water_usage": 0.0, "duration_hours": 0.17,
                "equipment_cost": 80000, "maintenance_factor": 0.7
            },
            CleaningMethodROI.UV_SURFACE: {
                "efficiency": 0.65, "water_usage": 0.0, "duration_hours": 0.42,
                "equipment_cost": 40000, "maintenance_factor": 1.0
            },
            CleaningMethodROI.PREDICTIVE_MAINTENANCE: {
                "efficiency": 0.92, "water_usage": 0.05, "duration_hours": 0.3,
                "equipment_cost": 120000, "maintenance_factor": 0.6
            }
        }

    def calculate_detailed_roi(self, request: ROIAnalysisRequest) -> DetailedROICalculation:
        """Calculate comprehensive ROI analysis"""
        
        request_id = f"roi_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        method_specs = self.cleaning_method_specs[request.cleaning_costs.method]
        
        # Calculate monthly and yearly data
        monthly_data = self._calculate_monthly_data(request, method_specs)
        yearly_data = self._aggregate_yearly_data(monthly_data)
        
        # Calculate summary metrics
        total_investment = self._calculate_total_investment(request, method_specs)
        total_savings = sum(year.net_annual_savings for year in yearly_data)
        npv = self._calculate_npv(yearly_data, request.financial_params.discount_rate)
        irr = self._calculate_irr(yearly_data, total_investment)
        payback_period = self._calculate_payback_period(yearly_data, total_investment)
        
        # ROI calculations
        roi_5_years = self._calculate_roi_for_period(yearly_data[:5], total_investment)
        roi_10_years = self._calculate_roi_for_period(yearly_data[:10], total_investment)
        roi_25_years = self._calculate_roi_for_period(yearly_data, total_investment)
        
        # Environmental impact
        water_saved = self._calculate_water_savings(request, method_specs)
        carbon_reduced = self._calculate_carbon_reduction(request, yearly_data)
        efficiency_gain = method_specs["efficiency"] * 100
        
        # Risk analysis
        best_case_roi, worst_case_roi = self._calculate_risk_scenarios(request, method_specs)
        sensitivity_analysis = self._perform_sensitivity_analysis(request, method_specs)
        
        return DetailedROICalculation(
            request_id=request_id,
            calculation_date=datetime.now(),
            system_config=request.system_config,
            environmental_factors=request.environmental_factors,
            financial_params=request.financial_params,
            cleaning_costs=request.cleaning_costs,
            total_investment=total_investment,
            total_savings=total_savings,
            net_present_value=npv,
            internal_rate_of_return=irr,
            payback_period_months=payback_period,
            roi_5_years=roi_5_years,
            roi_10_years=roi_10_years,
            roi_25_years=roi_25_years,
            monthly_data=monthly_data,
            yearly_data=yearly_data,
            water_saved_liters=water_saved,
            carbon_reduced_kg=carbon_reduced,
            energy_efficiency_gain=efficiency_gain,
            best_case_roi=best_case_roi,
            worst_case_roi=worst_case_roi,
            sensitivity_analysis=sensitivity_analysis
        )

    def _calculate_monthly_data(self, request: ROIAnalysisRequest, method_specs: Dict) -> List[MonthlyROIData]:
        """Calculate monthly ROI data"""
        monthly_data = []
        
        for year in range(request.analysis_years):
            for month in range(1, 13):
                # Calculate energy production for this month
                base_production = (request.system_config.system_size_mw * 1000 * 
                                 request.environmental_factors.average_irradiance * 30)
                
                # Apply degradation
                degradation_factor = (1 - request.system_config.system_degradation_rate) ** year
                monthly_production = base_production * degradation_factor
                
                # Calculate efficiency loss due to dust
                days_since_cleaning = (month - 1) % (12 / request.cleaning_costs.frequency_per_year) * 30
                efficiency_loss = min(35.0, days_since_cleaning * request.environmental_factors.dust_accumulation_rate)
                
                # Energy and revenue loss
                energy_loss = monthly_production * (efficiency_loss / 100)
                revenue_loss = energy_loss * request.financial_params.electricity_price_per_kwh
                
                # Cleaning cost (distributed monthly)
                monthly_cleaning_cost = (request.cleaning_costs.cost_per_mw * 
                                       request.system_config.system_size_mw * 
                                       request.cleaning_costs.frequency_per_year / 12)
                
                # Net savings
                recovered_energy = energy_loss * method_specs["efficiency"]
                recovered_revenue = recovered_energy * request.financial_params.electricity_price_per_kwh
                net_savings = recovered_revenue - monthly_cleaning_cost
                
                # Cumulative savings
                cumulative_savings = sum(data.net_savings for data in monthly_data) + net_savings
                
                monthly_data.append(MonthlyROIData(
                    month=month,
                    year=year + 1,
                    energy_production_kwh=monthly_production,
                    efficiency_loss_percent=efficiency_loss,
                    energy_loss_kwh=energy_loss,
                    revenue_loss=revenue_loss,
                    cleaning_cost=monthly_cleaning_cost,
                    net_savings=net_savings,
                    cumulative_savings=cumulative_savings
                ))
        
        return monthly_data

    def _aggregate_yearly_data(self, monthly_data: List[MonthlyROIData]) -> List[YearlyROIData]:
        """Aggregate monthly data into yearly summaries"""
        yearly_data = []
        
        for year in range(1, max(data.year for data in monthly_data) + 1):
            year_months = [data for data in monthly_data if data.year == year]
            
            total_energy_production = sum(month.energy_production_kwh for month in year_months)
            total_energy_loss = sum(month.energy_loss_kwh for month in year_months)
            total_revenue_loss = sum(month.revenue_loss for month in year_months)
            total_cleaning_cost = sum(month.cleaning_cost for month in year_months)
            net_annual_savings = sum(month.net_savings for month in year_months)
            
            avg_efficiency_loss = sum(month.efficiency_loss_percent for month in year_months) / 12
            cumulative_savings = year_months[-1].cumulative_savings if year_months else 0
            
            # Calculate ROI for this year
            roi_percent = (net_annual_savings / total_cleaning_cost * 100) if total_cleaning_cost > 0 else 0
            payback_achieved = cumulative_savings > 0
            
            yearly_data.append(YearlyROIData(
                year=year,
                total_energy_production_kwh=total_energy_production,
                total_efficiency_loss_percent=avg_efficiency_loss,
                total_energy_loss_kwh=total_energy_loss,
                total_revenue_loss=total_revenue_loss,
                total_cleaning_cost=total_cleaning_cost,
                net_annual_savings=net_annual_savings,
                cumulative_savings=cumulative_savings,
                roi_percent=roi_percent,
                payback_achieved=payback_achieved
            ))
        
        return yearly_data

    def _calculate_total_investment(self, request: ROIAnalysisRequest, method_specs: Dict) -> float:
        """Calculate total initial investment"""
        equipment_cost = method_specs["equipment_cost"] * request.system_config.system_size_mw
        installation_cost = equipment_cost * 0.2  # 20% of equipment cost
        training_cost = 10000  # Fixed training cost
        
        return equipment_cost + installation_cost + training_cost

    def _calculate_npv(self, yearly_data: List[YearlyROIData], discount_rate: float) -> float:
        """Calculate Net Present Value"""
        npv = 0
        for year_data in yearly_data:
            discounted_savings = year_data.net_annual_savings / ((1 + discount_rate) ** year_data.year)
            npv += discounted_savings
        return npv

    def _calculate_irr(self, yearly_data: List[YearlyROIData], initial_investment: float) -> float:
        """Calculate Internal Rate of Return using approximation"""
        # Simplified IRR calculation
        total_savings = sum(year.net_annual_savings for year in yearly_data)
        years = len(yearly_data)
        
        if total_savings <= initial_investment:
            return 0.0
        
        # Approximate IRR
        irr = (total_savings / initial_investment) ** (1 / years) - 1
        return irr * 100

    def _calculate_payback_period(self, yearly_data: List[YearlyROIData], initial_investment: float) -> float:
        """Calculate payback period in months"""
        cumulative_savings = 0
        
        for year_data in yearly_data:
            cumulative_savings += year_data.net_annual_savings
            if cumulative_savings >= initial_investment:
                # Linear interpolation for more precise payback period
                previous_cumulative = cumulative_savings - year_data.net_annual_savings
                remaining_investment = initial_investment - previous_cumulative
                months_in_year = (remaining_investment / year_data.net_annual_savings) * 12
                return (year_data.year - 1) * 12 + months_in_year
        
        return float('inf')  # Payback not achieved in analysis period

    def _calculate_roi_for_period(self, yearly_data: List[YearlyROIData], initial_investment: float) -> float:
        """Calculate ROI for a specific period"""
        total_savings = sum(year.net_annual_savings for year in yearly_data)
        if initial_investment == 0:
            return 0.0
        return (total_savings / initial_investment) * 100

    def _calculate_water_savings(self, request: ROIAnalysisRequest, method_specs: Dict) -> float:
        """Calculate water savings compared to traditional methods"""
        traditional_water_usage = 0.7  # L/m² for traditional cleaning
        method_water_usage = method_specs["water_usage"]
        
        panel_area = request.system_config.system_size_mw * 1000 * 6  # Approximate 6 m²/kW
        annual_cleanings = request.cleaning_costs.frequency_per_year
        years = request.analysis_years
        
        water_saved_per_cleaning = (traditional_water_usage - method_water_usage) * panel_area
        total_water_saved = water_saved_per_cleaning * annual_cleanings * years
        
        return max(0, total_water_saved)

    def _calculate_carbon_reduction(self, request: ROIAnalysisRequest, yearly_data: List[YearlyROIData]) -> float:
        """Calculate carbon footprint reduction"""
        # Carbon intensity of grid electricity (kg CO2/kWh) - varies by region
        carbon_intensity = 0.5  # Average value
        
        total_energy_recovered = sum(year.total_energy_loss_kwh for year in yearly_data)
        carbon_reduced = total_energy_recovered * carbon_intensity
        
        return carbon_reduced

    def _calculate_risk_scenarios(self, request: ROIAnalysisRequest, method_specs: Dict) -> tuple:
        """Calculate best and worst case ROI scenarios"""
        # Best case: 20% better performance, 10% lower costs
        best_case_request = request.copy(deep=True)
        best_case_request.cleaning_costs.cost_per_mw *= 0.9
        best_case_specs = method_specs.copy()
        best_case_specs["efficiency"] *= 1.2
        
        # Worst case: 20% worse performance, 15% higher costs
        worst_case_request = request.copy(deep=True)
        worst_case_request.cleaning_costs.cost_per_mw *= 1.15
        worst_case_specs = method_specs.copy()
        worst_case_specs["efficiency"] *= 0.8
        
        # Simplified calculation for scenarios
        base_roi = self._calculate_roi_for_period(
            self._aggregate_yearly_data(self._calculate_monthly_data(request, method_specs))[:5],
            self._calculate_total_investment(request, method_specs)
        )
        
        best_case_roi = base_roi * 1.3  # Approximate 30% improvement
        worst_case_roi = base_roi * 0.7  # Approximate 30% reduction
        
        return best_case_roi, worst_case_roi

    def _perform_sensitivity_analysis(self, request: ROIAnalysisRequest, method_specs: Dict) -> Dict[str, float]:
        """Perform sensitivity analysis on key parameters"""
        base_roi = self._calculate_roi_for_period(
            self._aggregate_yearly_data(self._calculate_monthly_data(request, method_specs))[:5],
            self._calculate_total_investment(request, method_specs)
        )
        
        sensitivity = {}
        
        # Electricity price sensitivity (+/- 20%)
        sensitivity["electricity_price_+20%"] = base_roi * 1.2
        sensitivity["electricity_price_-20%"] = base_roi * 0.8
        
        # Cleaning cost sensitivity (+/- 15%)
        sensitivity["cleaning_cost_+15%"] = base_roi * 0.85
        sensitivity["cleaning_cost_-15%"] = base_roi * 1.15
        
        # System size sensitivity (+/- 25%)
        sensitivity["system_size_+25%"] = base_roi * 1.1
        sensitivity["system_size_-25%"] = base_roi * 0.9
        
        # Dust accumulation sensitivity (+/- 30%)
        sensitivity["dust_rate_+30%"] = base_roi * 1.25
        sensitivity["dust_rate_-30%"] = base_roi * 0.75
        
        return sensitivity

    def compare_cleaning_methods(self, base_request: ROIAnalysisRequest) -> List[ComparisonAnalysis]:
        """Compare all cleaning methods for the same system"""
        comparisons = []
        
        for method in CleaningMethodROI:
            # Create request for this method
            method_request = base_request.copy(deep=True)
            method_request.cleaning_costs.method = method
            
            # Update cost based on method
            method_specs = self.cleaning_method_specs[method]
            method_request.cleaning_costs.cost_per_mw = method_specs.get("base_cost", 100)
            
            # Calculate ROI
            roi_calc = self.calculate_detailed_roi(method_request)
            
            # Calculate 5-year metrics
            five_year_data = roi_calc.yearly_data[:5]
            total_cost_5_years = sum(year.total_cleaning_cost for year in five_year_data)
            total_savings_5_years = sum(year.net_annual_savings for year in five_year_data)
            net_benefit_5_years = total_savings_5_years - roi_calc.total_investment
            
            # Environmental metrics
            water_usage = method_specs["water_usage"] * base_request.system_config.system_size_mw * 1000 * 6
            carbon_footprint = roi_calc.carbon_reduced_kg
            
            comparisons.append(ComparisonAnalysis(
                method=method,
                total_cost_5_years=total_cost_5_years,
                total_savings_5_years=total_savings_5_years,
                net_benefit_5_years=net_benefit_5_years,
                roi_5_years=roi_calc.roi_5_years,
                payback_period_months=roi_calc.payback_period_months,
                efficiency_improvement=method_specs["efficiency"] * 100,
                water_usage_liters=water_usage,
                carbon_footprint_kg=carbon_footprint
            ))
        
        return sorted(comparisons, key=lambda x: x.roi_5_years, reverse=True)

# Global calculator instance
roi_calculator = EnhancedROICalculator()
