#!/usr/bin/env python3
"""
Enhanced YOLO Training Script for Solar Panel Defect Detection
Supports multiple defect types: dust, cracks, hotspots, soiling, and degradation
"""

import os
import yaml
import torch
import numpy as np
from ultralytics import YOLO
from pathlib import Path
import logging
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import cv2
import albumentations as A
from albumentations.pytorch import ToTensorV2

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SolarPanelDataset:
    """Enhanced dataset class for solar panel defect detection"""
    
    def __init__(self, data_path, augment=True):
        self.data_path = Path(data_path)
        self.augment = augment
        self.classes = [
            'clean_panel',
            'dust_accumulation', 
            'crack_defect',
            'hotspot_thermal',
            'soiling_bird_droppings',
            'degradation_discoloration',
            'shadow_obstruction',
            'corrosion_frame',
            'glass_breakage'
        ]
        
        # Enhanced augmentation pipeline
        self.transform = <PERSON><PERSON>([
            A.RandomRotate90(p=0.3),
            <PERSON><PERSON>(p=0.3),
            <PERSON><PERSON>RandomBrightnessContrast(
                brightness_limit=0.2, 
                contrast_limit=0.2, 
                p=0.5
            ),
            A.HueSaturationValue(
                hue_shift_limit=10,
                sat_shift_limit=20,
                val_shift_limit=20,
                p=0.3
            ),
            A.GaussianBlur(blur_limit=3, p=0.2),
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.2),
            A.RandomShadow(p=0.3),
            A.RandomSunFlare(p=0.1),
            A.RandomFog(p=0.1),
            A.Resize(640, 640),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ]) if augment else A.Compose([
            A.Resize(640, 640),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])

def create_dataset_yaml():
    """Create YAML configuration for the dataset"""
    
    dataset_config = {
        'path': './datasets/solar_panels',
        'train': 'images/train',
        'val': 'images/val',
        'test': 'images/test',
        'nc': 9,  # number of classes
        'names': [
            'clean_panel',
            'dust_accumulation', 
            'crack_defect',
            'hotspot_thermal',
            'soiling_bird_droppings',
            'degradation_discoloration',
            'shadow_obstruction',
            'corrosion_frame',
            'glass_breakage'
        ]
    }
    
    with open('model/solar_dataset.yaml', 'w') as f:
        yaml.dump(dataset_config, f, default_flow_style=False)
    
    logger.info("Dataset YAML configuration created")
    return 'model/solar_dataset.yaml'

def train_enhanced_model():
    """Train enhanced YOLO model with multiple configurations"""
    
    # Create dataset configuration
    dataset_yaml = create_dataset_yaml()
    
    # Training configurations for different scenarios
    training_configs = {
        'high_accuracy': {
            'model': 'yolov8x.pt',  # Largest model for best accuracy
            'epochs': 300,
            'imgsz': 1280,
            'batch': 8,
            'lr0': 0.001,
            'weight_decay': 0.0005,
            'mosaic': 1.0,
            'mixup': 0.1,
            'copy_paste': 0.1,
            'augment': True
        },
        'balanced': {
            'model': 'yolov8l.pt',  # Large model for good balance
            'epochs': 200,
            'imgsz': 640,
            'batch': 16,
            'lr0': 0.01,
            'weight_decay': 0.0005,
            'mosaic': 1.0,
            'mixup': 0.05,
            'copy_paste': 0.05,
            'augment': True
        },
        'fast_inference': {
            'model': 'yolov8n.pt',  # Nano model for speed
            'epochs': 150,
            'imgsz': 416,
            'batch': 32,
            'lr0': 0.01,
            'weight_decay': 0.0005,
            'mosaic': 0.5,
            'mixup': 0.0,
            'copy_paste': 0.0,
            'augment': True
        }
    }
    
    results = {}
    
    for config_name, config in training_configs.items():
        logger.info(f"Training {config_name} model...")
        
        # Initialize model
        model = YOLO(config['model'])
        
        # Train model
        results[config_name] = model.train(
            data=dataset_yaml,
            epochs=config['epochs'],
            imgsz=config['imgsz'],
            batch=config['batch'],
            lr0=config['lr0'],
            weight_decay=config['weight_decay'],
            mosaic=config['mosaic'],
            mixup=config['mixup'],
            copy_paste=config['copy_paste'],
            augment=config['augment'],
            project='model/runs',
            name=f'solar_defect_{config_name}',
            save=True,
            save_period=50,
            cache=True,
            device='0' if torch.cuda.is_available() else 'cpu',
            workers=8,
            patience=50,
            plots=True,
            val=True,
            split_fraction=0.1,
            seed=42
        )
        
        # Save model with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_path = f'model/weights/solar_defect_{config_name}_{timestamp}.pt'
        model.save(model_path)
        
        logger.info(f"Model saved: {model_path}")
    
    return results

def evaluate_model(model_path, test_data_path):
    """Comprehensive model evaluation"""
    
    logger.info(f"Evaluating model: {model_path}")
    
    # Load model
    model = YOLO(model_path)
    
    # Run validation
    metrics = model.val(
        data='model/solar_dataset.yaml',
        split='test',
        save_json=True,
        save_hybrid=True,
        conf=0.25,
        iou=0.6,
        max_det=300,
        half=True,
        device='0' if torch.cuda.is_available() else 'cpu',
        dnn=False,
        plots=True,
        rect=False,
        save_txt=True,
        save_conf=True,
        save_crop=True,
        show_labels=True,
        show_conf=True,
        visualize=False,
        augment=False,
        agnostic_nms=False,
        retina_masks=False,
        format='torchscript',
        keras=False,
        optimize=False,
        int8=False,
        dynamic=False,
        simplify=False,
        opset=None,
        workspace=4,
        nms=False,
        lr0=0.01,
        lrf=0.01,
        momentum=0.937,
        weight_decay=0.0005,
        warmup_epochs=3.0,
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        box=7.5,
        cls=0.5,
        dfl=1.5,
        pose=12.0,
        kobj=2.0,
        label_smoothing=0.0,
        nbs=64,
        hsv_h=0.015,
        hsv_s=0.7,
        hsv_v=0.4,
        degrees=0.0,
        translate=0.1,
        scale=0.5,
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=1.0,
        mixup=0.0,
        copy_paste=0.0
    )
    
    # Print detailed metrics
    logger.info("Model Evaluation Results:")
    logger.info(f"mAP50: {metrics.box.map50:.4f}")
    logger.info(f"mAP50-95: {metrics.box.map:.4f}")
    logger.info(f"Precision: {metrics.box.mp:.4f}")
    logger.info(f"Recall: {metrics.box.mr:.4f}")
    
    return metrics

def create_thermal_integration():
    """Create thermal imaging integration for hotspot detection"""
    
    thermal_config = {
        'flir_integration': {
            'temperature_threshold': 85.0,  # Celsius
            'hotspot_detection': True,
            'thermal_overlay': True,
            'temperature_mapping': {
                'normal': [20, 45],
                'warm': [45, 65],
                'hot': [65, 85],
                'critical': [85, 120]
            }
        },
        'processing': {
            'gaussian_blur': 3,
            'morphological_ops': True,
            'contour_detection': True,
            'area_threshold': 100
        }
    }
    
    with open('model/thermal_config.yaml', 'w') as f:
        yaml.dump(thermal_config, f, default_flow_style=False)
    
    logger.info("Thermal integration configuration created")

def optimize_for_deployment():
    """Optimize models for different deployment scenarios"""
    
    model_paths = [
        'model/weights/solar_defect_high_accuracy_latest.pt',
        'model/weights/solar_defect_balanced_latest.pt',
        'model/weights/solar_defect_fast_inference_latest.pt'
    ]
    
    for model_path in model_paths:
        if os.path.exists(model_path):
            model = YOLO(model_path)
            
            # Export to different formats
            model.export(format='onnx', optimize=True, half=True)
            model.export(format='torchscript', optimize=True)
            model.export(format='tflite', int8=True)
            model.export(format='openvino', half=True)
            
            logger.info(f"Exported {model_path} to multiple formats")

def generate_training_report():
    """Generate comprehensive training report"""
    
    report = {
        'training_date': datetime.now().isoformat(),
        'models_trained': ['high_accuracy', 'balanced', 'fast_inference'],
        'dataset_info': {
            'classes': 9,
            'augmentation': 'Enhanced with weather conditions',
            'image_size': [416, 640, 1280],
            'total_epochs': [150, 200, 300]
        },
        'performance_targets': {
            'dust_detection': '>98%',
            'crack_detection': '>95%',
            'hotspot_detection': '>97%',
            'overall_map50': '>90%'
        },
        'deployment_formats': [
            'PyTorch (.pt)',
            'ONNX (.onnx)',
            'TensorFlow Lite (.tflite)',
            'OpenVINO (.xml/.bin)',
            'TorchScript (.torchscript)'
        ]
    }
    
    with open('model/training_report.yaml', 'w') as f:
        yaml.dump(report, f, default_flow_style=False)
    
    logger.info("Training report generated")

def main():
    """Main training pipeline"""
    
    logger.info("Starting Enhanced Solar Panel Defect Detection Training")
    
    # Create necessary directories
    os.makedirs('model/weights', exist_ok=True)
    os.makedirs('model/runs', exist_ok=True)
    os.makedirs('datasets/solar_panels/images/train', exist_ok=True)
    os.makedirs('datasets/solar_panels/images/val', exist_ok=True)
    os.makedirs('datasets/solar_panels/images/test', exist_ok=True)
    os.makedirs('datasets/solar_panels/labels/train', exist_ok=True)
    os.makedirs('datasets/solar_panels/labels/val', exist_ok=True)
    os.makedirs('datasets/solar_panels/labels/test', exist_ok=True)
    
    # Train models
    training_results = train_enhanced_model()
    
    # Create thermal integration
    create_thermal_integration()
    
    # Optimize for deployment
    optimize_for_deployment()
    
    # Generate report
    generate_training_report()
    
    logger.info("Enhanced training pipeline completed successfully!")
    logger.info("Models ready for deployment with 9 cleaning methods integration")

if __name__ == "__main__":
    main()
