<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar AI Cleaning & Monitoring System - Professional Presentation</title>

    <!-- Reveal.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/theme/white.css">

    <!-- Professional Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #27ae60;
            --accent-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-color: #2c3e50;
        }

        .reveal {
            font-family: 'Inter', 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .reveal h1, .reveal h2, .reveal h3 {
            color: var(--dark-color);
            font-weight: 700;
        }

        .reveal .slides section {
            text-align: left;
        }

        .title-slide {
            text-align: center !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
        }

        .title-slide h1, .title-slide h2, .title-slide p {
            color: white !important;
        }

        .method-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .method-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid var(--primary-color);
            transition: transform 0.3s ease;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .method-card.new {
            border-left-color: var(--accent-color);
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }

        .method-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            min-width: 150px;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--dark-color);
            font-weight: 500;
        }

        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .gantt-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .gantt-bar {
            height: 30px;
            margin: 10px 0;
            border-radius: 5px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: bold;
        }

        .phase1 { background: var(--primary-color); }
        .phase2 { background: var(--secondary-color); }
        .phase3 { background: var(--accent-color); }
        .phase4 { background: var(--danger-color); }

        .roi-highlight {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .sensor-widget {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        .sensor-value {
            font-size: 1.5em;
            font-weight: bold;
            color: var(--primary-color);
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            font-weight: bold;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .highlight-green { color: var(--secondary-color); font-weight: bold; }
        .highlight-red { color: var(--danger-color); font-weight: bold; }
        .highlight-blue { color: var(--primary-color); font-weight: bold; }

        .reveal .progress {
            color: var(--primary-color);
        }

        .reveal .controls {
            color: var(--primary-color);
        }
    </style>
</head>

<body>
    <div class="reveal">
        <div class="slides">
            
            <!-- Title Slide -->
            <section class="title-slide">
                <h1>Solar AI Cleaning & Monitoring System</h1>
                <h2>Advanced Multi-Method Cleaning Solutions</h2>
                <p>Revolutionizing Solar Panel Maintenance with AI-Powered Technology</p>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">9</div>
                        <div class="stat-label">Cleaning Methods</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">99%</div>
                        <div class="stat-label">AI Accuracy</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50%</div>
                        <div class="stat-label">Cost Reduction</div>
                    </div>
                </div>
            </section>

            <!-- Problem Statement -->
            <section>
                <h2>The Challenge</h2>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">35%</div>
                        <div class="stat-label">Efficiency Loss</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">$2M</div>
                        <div class="stat-label">Annual Losses</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">70%</div>
                        <div class="stat-label">Water Waste</div>
                    </div>
                </div>
                
                <ul>
                    <li>Dust accumulation reduces solar panel efficiency by up to 35%</li>
                    <li>Traditional cleaning methods are water-intensive and costly</li>
                    <li>Manual inspection misses critical defects</li>
                    <li>Reactive maintenance leads to extended downtime</li>
                    <li>Limited cleaning options for different environmental conditions</li>
                </ul>
            </section>

            <!-- Enhanced Cleaning Methods -->
            <section>
                <h2>9 Advanced Cleaning Methods</h2>
                <div class="method-grid">
                    <div class="method-card">
                        <div class="method-icon">🚁</div>
                        <h3>Drone Water Cleaning</h3>
                        <p>Traditional water-based drone cleaning</p>
                        <small>Efficiency: 85% | Water: 0.7 L/m²</small>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">⚡</div>
                        <h3>Drone Waterless</h3>
                        <p>Advanced waterless drone technology</p>
                        <small>Efficiency: 90% | Water: 0.0 L/m²</small>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">🤖</div>
                        <h3>Crawler Robots</h3>
                        <p>Autonomous panel-crawling robots</p>
                        <small>Efficiency: 95% | Water: 0.1 L/m²</small>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">✨</div>
                        <h3>Nano-Coatings</h3>
                        <p>Self-cleaning hydrophobic coatings</p>
                        <small>Efficiency: 75% | Water: 0.0 L/m²</small>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">💨</div>
                        <h3>Air Blowers</h3>
                        <p>High-pressure compressed air system</p>
                        <small>Efficiency: 70% | Water: 0.0 L/m²</small>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">🌊</div>
                        <h3>Ultrasonic Vibrations</h3>
                        <p>Frequency-based dust removal</p>
                        <small>Efficiency: 80% | Water: 0.0 L/m²</small>
                    </div>
                    
                    <div class="method-card new">
                        <div class="method-icon">⚡</div>
                        <h3>Electrostatic Cleaning</h3>
                        <p><strong>NEW:</strong> Electrostatic charge repulsion</p>
                        <small>Efficiency: 85% | Water: 0.0 L/m²</small>
                    </div>
                    
                    <div class="method-card new">
                        <div class="method-icon">☀️</div>
                        <h3>UV Surface Cleaning</h3>
                        <p><strong>NEW:</strong> UV light organic treatment</p>
                        <small>Efficiency: 65% | Water: 0.0 L/m²</small>
                    </div>
                    
                    <div class="method-card new">
                        <div class="method-icon">🧠</div>
                        <h3>Predictive Maintenance</h3>
                        <p><strong>NEW:</strong> AI-driven scheduling</p>
                        <small>Optimizes all methods based on conditions</small>
                    </div>
                </div>
            </section>

            <!-- Method Comparison -->
            <section>
                <h2>Cleaning Method Comparison</h2>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Efficiency</th>
                            <th>Water Usage</th>
                            <th>Cost/MW</th>
                            <th>Duration</th>
                            <th>Weather Dependent</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Crawler Robots</td>
                            <td class="highlight-green">95%</td>
                            <td class="highlight-green">0.1 L/m²</td>
                            <td class="highlight-green">$80</td>
                            <td>60 min</td>
                            <td class="highlight-green">No</td>
                        </tr>
                        <tr>
                            <td>Drone Waterless</td>
                            <td class="highlight-green">90%</td>
                            <td class="highlight-green">0.0 L/m²</td>
                            <td>$120</td>
                            <td class="highlight-green">30 min</td>
                            <td class="highlight-green">No</td>
                        </tr>
                        <tr>
                            <td>Electrostatic</td>
                            <td class="highlight-blue">85%</td>
                            <td class="highlight-green">0.0 L/m²</td>
                            <td class="highlight-green">$90</td>
                            <td class="highlight-green">10 min</td>
                            <td class="highlight-green">No</td>
                        </tr>
                        <tr>
                            <td>Drone Water</td>
                            <td class="highlight-blue">85%</td>
                            <td class="highlight-red">0.7 L/m²</td>
                            <td>$150</td>
                            <td>45 min</td>
                            <td class="highlight-red">Yes</td>
                        </tr>
                        <tr>
                            <td>Ultrasonic</td>
                            <td class="highlight-blue">80%</td>
                            <td class="highlight-green">0.0 L/m²</td>
                            <td>$100</td>
                            <td class="highlight-green">15 min</td>
                            <td class="highlight-green">No</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- IoT Sensor Integration -->
            <section>
                <h2>Live IoT Sensor Integration</h2>
                <div class="stats-container">
                    <div class="sensor-widget">
                        <div class="sensor-value">42.3°C</div>
                        <div>Temperature</div>
                    </div>
                    <div class="sensor-widget">
                        <div class="sensor-value">23%</div>
                        <div>Humidity</div>
                    </div>
                    <div class="sensor-widget">
                        <div class="sensor-value">12.4V</div>
                        <div>Voltage</div>
                    </div>
                    <div class="sensor-widget">
                        <div class="sensor-value">850W</div>
                        <div>Power Output</div>
                    </div>
                </div>
                
                <h3>Real-time Monitoring Features:</h3>
                <ul>
                    <li><strong>Temperature Sensors:</strong> Monitor panel heating and efficiency</li>
                    <li><strong>Humidity Sensors:</strong> Predict condensation and cleaning needs</li>
                    <li><strong>Voltage/Current:</strong> Real-time performance monitoring</li>
                    <li><strong>Vibration Sensors:</strong> Detect mechanical issues</li>
                    <li><strong>Dust Density:</strong> Environmental dust level monitoring</li>
                    <li><strong>Weather Integration:</strong> CAMS atmospheric data</li>
                </ul>
            </section>

            <!-- ROI Analysis -->
            <section>
                <h2>Enhanced ROI Analysis</h2>
                <div class="roi-highlight">
                    <h3>5-Year ROI: $8.2M Net Benefit</h3>
                    <p>100MW Installation with Multi-Method Approach</p>
                </div>
                
                <div class="chart-container">
                    <canvas id="roiChart" width="800" height="400"></canvas>
                </div>
                
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">18</div>
                        <div class="stat-label">Months Payback</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">410%</div>
                        <div class="stat-label">5-Year ROI</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">$1.6M</div>
                        <div class="stat-label">Annual Savings</div>
                    </div>
                </div>
            </section>

            <!-- AI-Powered Predictive Scheduling -->
            <section>
                <h2>AI-Powered Predictive Scheduling</h2>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">95%</div>
                        <div class="stat-label">Prediction Accuracy</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">30%</div>
                        <div class="stat-label">Efficiency Improvement</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Monitoring</div>
                    </div>
                </div>

                <div class="gantt-container">
                    <h3>Smart Cleaning Schedule - Next 30 Days</h3>
                    <div class="gantt-bar phase1">Week 1: Electrostatic Cleaning (Panels A1-A50) - Optimal Weather</div>
                    <div class="gantt-bar phase2">Week 2: Crawler Robots (Panels B1-B30) - High Dust Forecast</div>
                    <div class="gantt-bar phase3">Week 3: UV Treatment (Panels C1-C40) - Organic Contamination</div>
                    <div class="gantt-bar phase4">Week 4: Drone Waterless (Panels D1-D60) - Maintenance Window</div>
                </div>

                <h3>Advanced Predictive Factors:</h3>
                <ul>
                    <li><strong>Weather Intelligence:</strong> CAMS atmospheric dust predictions with 95% accuracy</li>
                    <li><strong>Performance Analytics:</strong> Real-time efficiency monitoring and trend analysis</li>
                    <li><strong>Seasonal Intelligence:</strong> Historical dust accumulation patterns and seasonal variations</li>
                    <li><strong>Cost Optimization:</strong> Dynamic method selection based on ROI and resource availability</li>
                    <li><strong>Resource Management:</strong> Intelligent equipment and crew scheduling optimization</li>
                    <li><strong>Maintenance Prediction:</strong> Proactive maintenance scheduling to prevent failures</li>
                </ul>
            </section>

            <!-- Technology Integration -->
            <section>
                <h2>Complete Technology Stack</h2>
                <div class="method-grid">
                    <div class="method-card">
                        <div class="method-icon">🤖</div>
                        <h3>AI Detection</h3>
                        <p>YOLOv9 + Thermal Imaging</p>
                        <small>99% accuracy in defect detection</small>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">📱</div>
                        <h3>Mobile App</h3>
                        <p>React Native cross-platform</p>
                        <small>Real-time monitoring & control</small>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">🌐</div>
                        <h3>Web Dashboard</h3>
                        <p>React + FastAPI backend</p>
                        <small>Comprehensive management interface</small>
                    </div>
                    
                    <div class="method-card">
                        <div class="method-icon">📡</div>
                        <h3>IoT Integration</h3>
                        <p>Arduino + Raspberry Pi</p>
                        <small>Real-time sensor data collection</small>
                    </div>
                </div>
            </section>

            <!-- Competitive Advantage -->
            <section>
                <h2>Competitive Advantages</h2>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">9</div>
                        <div class="stat-label">Cleaning Methods</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1st</div>
                        <div class="stat-label">AI Integration</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">85%</div>
                        <div class="stat-label">Water Savings</div>
                    </div>
                </div>
                
                <h3>Unique Features:</h3>
                <ul>
                    <li><strong>Multi-Method Approach:</strong> 9 different cleaning technologies</li>
                    <li><strong>Predictive AI:</strong> Smart scheduling based on conditions</li>
                    <li><strong>Real-time IoT:</strong> Live sensor monitoring and alerts</li>
                    <li><strong>Mobile Integration:</strong> Professional cross-platform app</li>
                    <li><strong>Cost Optimization:</strong> Method selection for maximum ROI</li>
                    <li><strong>Environmental Focus:</strong> Waterless and eco-friendly options</li>
                </ul>
            </section>

            <!-- Call to Action -->
            <section class="title-slide">
                <h1>Transform Your Solar Operations</h1>
                <h2>Join the Future of Solar Maintenance</h2>
                
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">50%</div>
                        <div class="stat-label">Cost Reduction</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">25%</div>
                        <div class="stat-label">Efficiency Gain</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">85%</div>
                        <div class="stat-label">Water Savings</div>
                    </div>
                </div>
                
                <p style="font-size: 1.2em; margin-top: 30px;">
                    📧 <EMAIL><br>
                    📱 +966 50 123 4567<br>
                    🌐 www.solar-ai-monitoring.com
                </p>
            </section>

        </div>
    </div>

    <!-- Reveal.js JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.js"></script>
    <script>
        // Initialize Reveal.js
        Reveal.initialize({
            hash: true,
            transition: 'fade',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            controls: true,
            progress: true,
            center: false,
            touch: true,
            loop: false,
            rtl: false,
            navigationMode: 'default',
            shuffle: false,
            fragments: true,
            embedded: false,
            help: true,
            showNotes: false,
            autoPlayMedia: null,
            preloadIframes: null,
            autoSlide: 0,
            autoSlideStoppable: true,
            mouseWheel: false,
            hideInactiveCursor: true,
            hideCursorTime: 5000,
            previewLinks: false,
            focusBodyOnPageVisibilityChange: true,
            viewDistance: 3,
            mobileViewDistance: 2,
            display: 'block',
            hideAddressBar: true,
            parallaxBackgroundImage: '',
            parallaxBackgroundSize: '',
            parallaxBackgroundRepeat: '',
            parallaxBackgroundPosition: '',
            parallaxBackgroundHorizontal: null,
            parallaxBackgroundVertical: null
        });

        // Initialize ROI Chart
        Reveal.addEventListener('slidechanged', function(event) {
            if (event.currentSlide.querySelector('#roiChart')) {
                initROIChart();
            }
        });

        function initROIChart() {
            const ctx = document.getElementById('roiChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Traditional', 'Single Method', 'Multi-Method AI'],
                    datasets: [{
                        label: 'Annual Savings ($M)',
                        data: [0.5, 1.2, 1.6],
                        backgroundColor: ['#e74c3c', '#f39c12', '#27ae60'],
                        borderColor: ['#c0392b', '#e67e22', '#229954'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Annual Savings Comparison (100MW Installation)'
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Annual Savings (Million $)'
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
