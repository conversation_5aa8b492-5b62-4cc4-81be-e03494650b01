#!/usr/bin/env python3
"""
Script to generate Arabic PowerPoint presentation
Runs the Arabic PowerPoint generator and creates the presentation file
"""

import sys
import os
import subprocess
from pathlib import Path

def install_requirements():
    """Install required packages for Arabic PowerPoint generation"""
    requirements = [
        "python-pptx",
        "arabic-reshaper", 
        "python-bidi",
        "pillow"
    ]
    
    for package in requirements:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")

def run_generator():
    """Run the Arabic PowerPoint generator"""
    try:
        # Change to slides directory
        slides_dir = Path(__file__).parent.parent / "slides"
        os.chdir(slides_dir)
        
        # Run the generator
        result = subprocess.run([
            sys.executable, "create_arabic_powerpoint.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Arabic PowerPoint generated successfully!")
            print(result.stdout)
        else:
            print("❌ Failed to generate Arabic PowerPoint")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Error running generator: {e}")

def main():
    """Main execution function"""
    print("🌞 Solar AI Monitoring - Arabic PowerPoint Generator")
    print("=" * 50)
    
    print("📦 Installing required packages...")
    install_requirements()
    
    print("\n🎨 Generating Arabic PowerPoint presentation...")
    run_generator()
    
    print("\n✨ Process completed!")
    print("📁 Check the slides/ directory for the generated presentation")

if __name__ == "__main__":
    main()
