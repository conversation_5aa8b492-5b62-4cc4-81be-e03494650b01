#!/usr/bin/env python3
"""
Enhanced Solar AI Cleaning & Monitoring System - FastAPI Backend
Features: Extended cleaning methods, predictive scheduling, IoT integration
"""

from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import json
import logging
import uvicorn

# Import authentication
from .auth import (
    authenticate_user, create_tokens_for_user, get_current_user,
    get_current_active_user, get_admin_user, require_permission,
    User, UserCreate, UserLogin, Token, UserRole, create_user,
    validate_password_strength, PasswordChange
)

# Import CAMS integration
from .cams_integration import (
    CAMSClient, DustForecast, WeatherData, DustAlert,
    create_dust_visualization_data
)

# Import notifications
from .notifications import (
    notification_service, NotificationRequest, NotificationTemplate,
    NotificationRecipient, NOTIFICATION_TEMPLATES, NOTIFICATION_RECIPIENTS
)

# Import enhanced ROI calculator
from .roi_calculator import (
    roi_calculator, ROIAnalysisRequest, DetailedROICalculation,
    ComparisonAnalysis, SystemConfiguration, EnvironmentalFactors,
    FinancialParameters, CleaningCosts, CleaningMethodROI
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Solar AI Cleaning & Monitoring API",
    description="""
    ## Advanced AI-powered Solar Panel Monitoring and Cleaning System

    This API provides comprehensive monitoring and management capabilities for solar panel installations with:

    ### 🌟 Key Features
    - **9 Advanced Cleaning Methods**: From traditional drones to cutting-edge electrostatic cleaning
    - **AI-Powered Predictions**: Smart scheduling based on weather and efficiency data
    - **Real-time IoT Integration**: Live sensor monitoring and alerts
    - **Multi-channel Notifications**: Email, SMS, Telegram, WhatsApp, and push notifications
    - **CAMS Weather Integration**: Atmospheric dust forecasting and analysis
    - **ROI Optimization**: Cost-benefit analysis for different cleaning methods

    ### 🔐 Authentication
    All endpoints require JWT authentication. Use `/api/auth/login` to obtain access tokens.

    ### 📊 Monitoring Capabilities
    - Real-time panel status and efficiency monitoring
    - Dust level tracking and forecasting
    - Battery and temperature monitoring
    - Defect detection and reporting

    ### 🤖 AI Features
    - YOLOv9-based defect detection
    - Thermal imaging integration
    - Predictive maintenance scheduling
    - Optimal cleaning method selection

    ### 🌍 Environmental Data
    - CAMS atmospheric dust forecasting
    - Weather data integration
    - Environmental impact analysis
    - Water usage optimization

    ### 📱 Integration Ready
    - RESTful API design
    - WebSocket support for real-time updates
    - Mobile app compatible
    - Third-party system integration
    """,
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    contact={
        "name": "Solar AI Monitoring Support",
        "url": "https://solar-ai-monitoring.com",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    }
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Enhanced Cleaning Methods Enum
class CleaningMethod(str, Enum):
    DRONE_WATER = "drone_water"
    DRONE_WATERLESS = "drone_waterless"
    CRAWLER_ROBOTS = "crawler_robots"
    NANO_COATINGS = "nano_coatings"
    AIR_BLOWERS = "air_blowers"
    ULTRASONIC = "ultrasonic_vibrations"
    ELECTROSTATIC = "electrostatic_cleaning"
    UV_SURFACE = "uv_surface_cleaning"
    PREDICTIVE_MAINTENANCE = "predictive_maintenance"

# Data models
class PanelDefect(BaseModel):
    type: str
    severity: float
    detected_at: str

class PanelLocation(BaseModel):
    lat: float
    lng: float

class PanelStatus(BaseModel):
    panel_id: str
    location: PanelLocation
    status: str  # clean, dusty, needs_attention
    dust_level: float  # percentage
    efficiency: float  # percentage
    last_cleaned: str
    defects: List[str]
    battery_level: Optional[float] = None
    temperature: Optional[float] = None
    cleaning_method: Optional[CleaningMethod] = None

class SensorData(BaseModel):
    sensor_id: str
    panel_id: str
    timestamp: datetime
    temperature: float
    humidity: float
    voltage: float
    current: float
    power: float
    vibration: Optional[float] = None
    dust_density: Optional[float] = None

class CleaningSchedule(BaseModel):
    schedule_id: str
    panel_ids: List[str]
    method: CleaningMethod
    scheduled_time: datetime
    estimated_duration: int  # minutes
    priority: str = Field(..., description="low, medium, high, critical")
    weather_dependent: bool = True
    cost_estimate: float

class ROICalculation(BaseModel):
    system_size_mw: float
    current_efficiency_loss: float
    electricity_price_per_kwh: float
    cleaning_frequency_per_year: int
    maintenance_cost_per_cleaning: float
    method: CleaningMethod
    annual_energy_loss_kwh: float
    annual_revenue_loss: float
    annual_cleaning_cost: float
    net_annual_savings: float
    payback_period_months: float
    five_year_roi: float

class MethodSelection(BaseModel):
    panel_ids: List[str]
    method: CleaningMethod
    priority: str = "medium"
    schedule_time: Optional[datetime] = None
    parameters: Dict[str, Any] = {}

# Mock data for demonstration
mock_panels = [
    PanelStatus(
        panel_id="A23",
        location=PanelLocation(lat=24.7136, lng=46.6753),
        status="clean",
        dust_level=5.0,
        efficiency=95.0,
        last_cleaned="2025-06-18",
        defects=[],
        battery_level=85.0,
        temperature=35.2
    ),
    PanelStatus(
        panel_id="B15",
        location=PanelLocation(lat=24.7140, lng=46.6760),
        status="dusty",
        dust_level=25.0,
        efficiency=75.0,
        last_cleaned="2025-06-10",
        defects=["dust"],
        battery_level=78.0,
        temperature=38.1
    ),
    PanelStatus(
        panel_id="C07",
        location=PanelLocation(lat=24.7145, lng=46.6765),
        status="needs_attention",
        dust_level=40.0,
        efficiency=60.0,
        last_cleaned="2025-06-05",
        defects=["dust", "crack"],
        battery_level=65.0,
        temperature=42.5
    )
]

@app.get("/", tags=["System"])
async def root():
    """
    API Root Endpoint

    Returns basic information about the Solar AI Cleaning & Monitoring API.
    """
    return {
        "message": "Solar AI Cleaning & Monitoring API",
        "version": "2.0.0",
        "features": [
            "Extended cleaning methods",
            "Predictive scheduling",
            "IoT sensor integration",
            "Real-time monitoring",
            "ROI optimization"
        ]
    }

@app.get("/api/panel-status", response_model=List[PanelStatus])
async def get_panel_status():
    """
    Get the status of all solar panels
    """
    return mock_panels

@app.get("/api/panel-status/{panel_id}", response_model=PanelStatus)
async def get_panel_status_by_id(panel_id: str):
    """
    Get the status of a specific solar panel by ID
    """
    for panel in mock_panels:
        if panel.panel_id == panel_id:
            return panel
    raise HTTPException(status_code=404, detail="Panel not found")

@app.post("/api/panel-status/{panel_id}/update")
async def update_panel_status(panel_id: str, status_update: dict):
    """
    Update the status of a specific solar panel
    """
    for i, panel in enumerate(mock_panels):
        if panel.panel_id == panel_id:
            # Update fields if provided
            if "dust_level" in status_update:
                mock_panels[i].dust_level = status_update["dust_level"]
            if "efficiency" in status_update:
                mock_panels[i].efficiency = status_update["efficiency"]
            if "status" in status_update:
                mock_panels[i].status = status_update["status"]
            if "defects" in status_update:
                mock_panels[i].defects = status_update["defects"]
            if "last_cleaned" in status_update:
                mock_panels[i].last_cleaned = status_update["last_cleaned"]
            
            return {"message": f"Panel {panel_id} updated successfully"}
    
    raise HTTPException(status_code=404, detail="Panel not found")

@app.get("/api/stats")
async def get_system_stats():
    """
    Get overall system statistics
    """
    total_panels = len(mock_panels)
    active_panels = len([p for p in mock_panels if p.status != "offline"])
    panels_needing_cleaning = len([p for p in mock_panels if p.status in ["dusty", "needs_attention"]])
    avg_efficiency = sum(p.efficiency for p in mock_panels) / total_panels if total_panels > 0 else 0
    avg_battery = sum(p.battery_level for p in mock_panels if p.battery_level) / total_panels if total_panels > 0 else 0
    
    return {
        "total_panels": total_panels,
        "active_panels": active_panels,
        "panels_needing_cleaning": panels_needing_cleaning,
        "average_efficiency": round(avg_efficiency, 2),
        "average_battery_level": round(avg_battery, 2),
        "last_updated": datetime.now().isoformat()
    }

@app.post("/api/cleaning/schedule")
async def schedule_cleaning(panel_ids: List[str]):
    """
    Schedule cleaning for specified panels
    """
    scheduled_panels = []
    for panel_id in panel_ids:
        for panel in mock_panels:
            if panel.panel_id == panel_id:
                scheduled_panels.append(panel_id)
                break
    
    if not scheduled_panels:
        raise HTTPException(status_code=404, detail="No valid panels found")
    
    return {
        "message": f"Cleaning scheduled for {len(scheduled_panels)} panels",
        "scheduled_panels": scheduled_panels,
        "scheduled_at": datetime.now().isoformat()
    }

# Enhanced Cleaning Method Configurations
CLEANING_METHODS = {
    CleaningMethod.DRONE_WATER: {
        "name": "Drone Water Cleaning",
        "description": "Traditional drone cleaning with water spray",
        "water_usage": 0.7,  # L/m²
        "efficiency": 85,
        "cost_per_mw": 150,
        "duration_minutes": 45,
        "weather_dependent": True
    },
    CleaningMethod.DRONE_WATERLESS: {
        "name": "Drone Waterless Cleaning",
        "description": "Advanced drone cleaning without water",
        "water_usage": 0.0,
        "efficiency": 90,
        "cost_per_mw": 120,
        "duration_minutes": 30,
        "weather_dependent": False
    },
    CleaningMethod.CRAWLER_ROBOTS: {
        "name": "Crawler Robots",
        "description": "Autonomous robots that crawl on panels",
        "water_usage": 0.1,
        "efficiency": 95,
        "cost_per_mw": 80,
        "duration_minutes": 60,
        "weather_dependent": False
    },
    CleaningMethod.NANO_COATINGS: {
        "name": "Self-Cleaning Nano-Coatings",
        "description": "Hydrophobic nano-coatings for self-cleaning",
        "water_usage": 0.0,
        "efficiency": 75,
        "cost_per_mw": 200,
        "duration_minutes": 0,  # Passive cleaning
        "weather_dependent": True
    },
    CleaningMethod.AIR_BLOWERS: {
        "name": "High-Pressure Air Blowers",
        "description": "Compressed air system for dust removal",
        "water_usage": 0.0,
        "efficiency": 70,
        "cost_per_mw": 60,
        "duration_minutes": 20,
        "weather_dependent": False
    },
    CleaningMethod.ULTRASONIC: {
        "name": "Ultrasonic Vibrations",
        "description": "Ultrasonic frequency vibrations to remove dust",
        "water_usage": 0.0,
        "efficiency": 80,
        "cost_per_mw": 100,
        "duration_minutes": 15,
        "weather_dependent": False
    },
    CleaningMethod.ELECTROSTATIC: {
        "name": "Electrostatic Cleaning",
        "description": "Electrostatic charge to repel dust particles",
        "water_usage": 0.0,
        "efficiency": 85,
        "cost_per_mw": 90,
        "duration_minutes": 10,
        "weather_dependent": False
    },
    CleaningMethod.UV_SURFACE: {
        "name": "UV Surface Cleaning",
        "description": "UV light treatment for organic contamination",
        "water_usage": 0.0,
        "efficiency": 65,
        "cost_per_mw": 70,
        "duration_minutes": 25,
        "weather_dependent": False
    }
}

# In-memory storage for enhanced features
sensors_db: Dict[str, List[SensorData]] = {}
schedules_db: Dict[str, CleaningSchedule] = {}

# Enhanced API Endpoints

@app.get("/api/cleaning/methods")
async def get_cleaning_methods():
    """Get all available cleaning methods with specifications"""
    return CLEANING_METHODS

@app.post("/api/select-method")
async def select_cleaning_method(selection: MethodSelection):
    """Select and configure cleaning method for panels"""
    method_config = CLEANING_METHODS.get(selection.method)
    if not method_config:
        raise HTTPException(status_code=400, detail="Invalid cleaning method")

    # Create cleaning schedule
    schedule_id = f"schedule_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    schedule = CleaningSchedule(
        schedule_id=schedule_id,
        panel_ids=selection.panel_ids,
        method=selection.method,
        scheduled_time=selection.schedule_time or datetime.now() + timedelta(hours=1),
        estimated_duration=method_config["duration_minutes"],
        priority=selection.priority,
        weather_dependent=method_config["weather_dependent"],
        cost_estimate=len(selection.panel_ids) * method_config["cost_per_mw"]
    )

    schedules_db[schedule_id] = schedule

    return {
        "message": "Cleaning method selected successfully",
        "schedule_id": schedule_id,
        "method": selection.method,
        "estimated_cost": schedule.cost_estimate,
        "estimated_duration": schedule.estimated_duration
    }

@app.post("/api/predict-schedule")
async def predict_cleaning_schedule(
    panel_ids: List[str],
    days_ahead: int = 7
):
    """Predict optimal cleaning schedule based on weather and efficiency data"""

    predicted_schedules = []

    for panel in mock_panels:
        if panel.panel_id not in panel_ids:
            continue

        # Simulate prediction logic
        dust_accumulation_rate = 5.0  # % per day
        current_dust = panel.dust_level

        # Predict when cleaning will be needed
        days_until_cleaning = (80 - current_dust) / dust_accumulation_rate

        if days_until_cleaning <= days_ahead:
            # Select optimal method based on conditions
            optimal_method = CleaningMethod.CRAWLER_ROBOTS
            if current_dust > 60:
                optimal_method = CleaningMethod.DRONE_WATERLESS
            elif current_dust < 30:
                optimal_method = CleaningMethod.AIR_BLOWERS

            schedule_time = datetime.now() + timedelta(days=max(1, int(days_until_cleaning)))

            predicted_schedules.append({
                "panel_id": panel.panel_id,
                "recommended_method": optimal_method,
                "scheduled_time": schedule_time,
                "urgency": "high" if days_until_cleaning <= 2 else "medium",
                "predicted_dust_level": current_dust + (dust_accumulation_rate * days_until_cleaning),
                "efficiency_impact": f"{(current_dust + (dust_accumulation_rate * days_until_cleaning)) * 0.35:.1f}%"
            })

    return {
        "prediction_period_days": days_ahead,
        "total_panels": len(panel_ids),
        "panels_needing_cleaning": len(predicted_schedules),
        "predicted_schedules": predicted_schedules,
        "generated_at": datetime.now()
    }

@app.get("/api/sensors/live")
async def get_live_sensor_data(panel_id: Optional[str] = None):
    """Get live sensor data from IoT devices"""
    if panel_id:
        return sensors_db.get(panel_id, [])

    # Return all sensor data
    all_sensors = {}
    for pid, sensors in sensors_db.items():
        all_sensors[pid] = sensors[-10:]  # Last 10 readings

    return all_sensors

@app.post("/api/sensors/data")
async def receive_sensor_data(sensor_data: SensorData):
    """Receive sensor data from IoT devices"""
    panel_id = sensor_data.panel_id

    if panel_id not in sensors_db:
        sensors_db[panel_id] = []

    sensors_db[panel_id].append(sensor_data)

    # Keep only last 1000 readings per panel
    if len(sensors_db[panel_id]) > 1000:
        sensors_db[panel_id] = sensors_db[panel_id][-1000:]

    return {"message": "Sensor data received successfully"}

@app.post("/api/roi/calculate", response_model=ROICalculation)
async def calculate_roi(
    system_size_mw: float,
    current_efficiency_loss: float,
    electricity_price_per_kwh: float,
    cleaning_frequency_per_year: int,
    method: CleaningMethod
):
    """Calculate ROI for different cleaning methods"""

    method_config = CLEANING_METHODS.get(method)
    if not method_config:
        raise HTTPException(status_code=400, detail="Invalid cleaning method")

    # ROI Calculations
    annual_energy_production_kwh = system_size_mw * 1000 * 8760 * 0.25  # 25% capacity factor
    annual_energy_loss_kwh = annual_energy_production_kwh * (current_efficiency_loss / 100)
    annual_revenue_loss = annual_energy_loss_kwh * electricity_price_per_kwh

    # Method-specific improvements
    efficiency_improvement = method_config["efficiency"] / 100
    recovered_energy = annual_energy_loss_kwh * efficiency_improvement
    recovered_revenue = recovered_energy * electricity_price_per_kwh

    annual_cleaning_cost = cleaning_frequency_per_year * method_config["cost_per_mw"] * system_size_mw
    net_annual_savings = recovered_revenue - annual_cleaning_cost

    payback_period_months = (method_config["cost_per_mw"] * system_size_mw) / (net_annual_savings / 12) if net_annual_savings > 0 else float('inf')
    five_year_roi = (net_annual_savings * 5) / (method_config["cost_per_mw"] * system_size_mw) * 100

    return ROICalculation(
        system_size_mw=system_size_mw,
        current_efficiency_loss=current_efficiency_loss,
        electricity_price_per_kwh=electricity_price_per_kwh,
        cleaning_frequency_per_year=cleaning_frequency_per_year,
        maintenance_cost_per_cleaning=method_config["cost_per_mw"],
        method=method,
        annual_energy_loss_kwh=annual_energy_loss_kwh,
        annual_revenue_loss=annual_revenue_loss,
        annual_cleaning_cost=annual_cleaning_cost,
        net_annual_savings=net_annual_savings,
        payback_period_months=payback_period_months,
        five_year_roi=five_year_roi
    )

# Enhanced ROI Calculator Endpoints

@app.post("/api/roi/calculate-detailed", response_model=DetailedROICalculation, tags=["Analytics"])
async def calculate_detailed_roi(
    request: ROIAnalysisRequest,
    current_user: dict = Depends(get_current_active_user)
):
    """
    Calculate comprehensive ROI analysis with detailed monthly/yearly breakdown

    This endpoint provides:
    - Detailed monthly and yearly financial projections
    - NPV and IRR calculations
    - Environmental impact analysis
    - Risk scenarios and sensitivity analysis
    - Payback period calculation
    """
    try:
        result = roi_calculator.calculate_detailed_roi(request)
        logger.info(f"Detailed ROI calculated by {current_user['username']} for {request.system_config.system_size_mw}MW system")
        return result
    except Exception as e:
        logger.error(f"Error calculating detailed ROI: {e}")
        raise HTTPException(status_code=500, detail=f"ROI calculation failed: {str(e)}")

@app.post("/api/roi/compare-methods", response_model=List[ComparisonAnalysis], tags=["Analytics"])
async def compare_cleaning_methods(
    base_request: ROIAnalysisRequest,
    current_user: dict = Depends(get_current_active_user)
):
    """
    Compare ROI for all 9 cleaning methods

    Returns a ranked list of cleaning methods based on:
    - 5-year ROI
    - Payback period
    - Environmental impact
    - Total cost and savings
    """
    try:
        comparisons = roi_calculator.compare_cleaning_methods(base_request)
        logger.info(f"Method comparison calculated by {current_user['username']} for {base_request.system_config.system_size_mw}MW system")
        return comparisons
    except Exception as e:
        logger.error(f"Error comparing cleaning methods: {e}")
        raise HTTPException(status_code=500, detail=f"Method comparison failed: {str(e)}")

@app.get("/api/roi/cleaning-methods", tags=["Analytics"])
async def get_cleaning_method_specs(
    current_user: dict = Depends(get_current_active_user)
):
    """Get specifications for all cleaning methods"""
    return {
        "methods": [method.value for method in CleaningMethodROI],
        "specifications": roi_calculator.cleaning_method_specs,
        "total_methods": len(CleaningMethodROI)
    }

@app.post("/api/roi/quick-estimate", tags=["Analytics"])
async def quick_roi_estimate(
    system_size_mw: float,
    location: str,
    electricity_price: float,
    method: CleaningMethodROI = CleaningMethodROI.DRONE_WATERLESS,
    current_user: dict = Depends(get_current_active_user)
):
    """
    Quick ROI estimate for basic analysis

    Simplified calculation for initial assessment
    """
    try:
        # Create simplified request
        system_config = SystemConfiguration(
            system_size_mw=system_size_mw,
            location=location,
            installation_year=2024,
            panel_type="monocrystalline"
        )

        environmental_factors = EnvironmentalFactors(
            average_irradiance=5.5,  # Default for Middle East
            dust_accumulation_rate=1.5  # Default moderate dust
        )

        financial_params = FinancialParameters(
            electricity_price_per_kwh=electricity_price
        )

        cleaning_costs = CleaningCosts(
            method=method,
            cost_per_mw=100,  # Default cost
            frequency_per_year=12
        )

        request = ROIAnalysisRequest(
            system_config=system_config,
            environmental_factors=environmental_factors,
            financial_params=financial_params,
            cleaning_costs=cleaning_costs,
            analysis_years=5
        )

        result = roi_calculator.calculate_detailed_roi(request)

        # Return simplified summary
        return {
            "system_size_mw": system_size_mw,
            "location": location,
            "method": method.value,
            "roi_5_years": result.roi_5_years,
            "payback_period_months": result.payback_period_months,
            "total_savings": result.total_savings,
            "total_investment": result.total_investment,
            "net_present_value": result.net_present_value,
            "water_saved_liters": result.water_saved_liters,
            "carbon_reduced_kg": result.carbon_reduced_kg,
            "calculation_date": result.calculation_date
        }

    except Exception as e:
        logger.error(f"Error in quick ROI estimate: {e}")
        raise HTTPException(status_code=500, detail=f"Quick estimate failed: {str(e)}")

# Authentication Endpoints

@app.post("/api/auth/login", response_model=Token)
async def login(user_credentials: UserLogin):
    """Authenticate user and return JWT tokens"""
    user = authenticate_user(user_credentials.username, user_credentials.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    tokens = create_tokens_for_user(user)
    logger.info(f"User {user['username']} logged in successfully")
    return tokens

@app.post("/api/auth/register", response_model=User)
async def register(user_data: UserCreate, admin_user: dict = Depends(get_admin_user)):
    """Register a new user (admin only)"""
    if not validate_password_strength(user_data.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be at least 8 characters with uppercase, lowercase, and numbers"
        )

    new_user = create_user(user_data)
    logger.info(f"New user {new_user['username']} created by admin {admin_user['username']}")

    return User(
        id=new_user["id"],
        username=new_user["username"],
        email=new_user["email"],
        full_name=new_user["full_name"],
        role=new_user["role"],
        is_active=new_user["is_active"],
        created_at=new_user["created_at"],
        last_login=new_user["last_login"]
    )

@app.get("/api/auth/me", response_model=User)
async def get_current_user_info(current_user: dict = Depends(get_current_active_user)):
    """Get current user information"""
    return User(
        id=current_user["id"],
        username=current_user["username"],
        email=current_user["email"],
        full_name=current_user["full_name"],
        role=current_user["role"],
        is_active=current_user["is_active"],
        created_at=current_user["created_at"],
        last_login=current_user["last_login"]
    )

@app.get("/api/health")
async def health_check():
    """System health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "services": {
            "api": "running",
            "database": "connected",
            "ai_models": "loaded",
            "sensors": "active"
        }
    }

# CAMS Dust Data Endpoints

@app.get("/api/dust/forecast")
async def get_dust_forecast(
    lat: float,
    lng: float,
    hours_ahead: int = 72,
    current_user: dict = Depends(get_current_active_user)
):
    """Get dust concentration forecast for location"""
    async with CAMSClient() as cams:
        forecasts = await cams.get_dust_forecast(lat, lng, hours_ahead)
        analysis = cams.analyze_dust_impact(forecasts)
        visualization_data = create_dust_visualization_data(forecasts)

        return {
            "location": {"lat": lat, "lng": lng},
            "forecasts": [f.dict() for f in forecasts],
            "analysis": analysis,
            "visualization": visualization_data,
            "generated_at": datetime.now()
        }

@app.get("/api/weather/current")
async def get_current_weather(
    lat: float,
    lng: float,
    hours_back: int = 24,
    current_user: dict = Depends(get_current_active_user)
):
    """Get current and historical weather data"""
    async with CAMSClient() as cams:
        weather_data = await cams.get_weather_data(lat, lng, hours_back)

        return {
            "location": {"lat": lat, "lng": lng},
            "weather_data": [w.dict() for w in weather_data],
            "summary": {
                "current_temperature": weather_data[-1].temperature if weather_data else None,
                "current_humidity": weather_data[-1].humidity if weather_data else None,
                "current_wind_speed": weather_data[-1].wind_speed if weather_data else None,
                "avg_temperature": sum(w.temperature for w in weather_data) / len(weather_data) if weather_data else None
            },
            "retrieved_at": datetime.now()
        }

@app.get("/api/dust/alerts")
async def get_dust_alerts(
    current_user: dict = Depends(get_current_active_user)
):
    """Get active dust alerts for all panel locations"""
    # Get panel locations from mock data
    panel_locations = [
        {"panel_id": panel.panel_id, "lat": panel.location.lat, "lng": panel.location.lng}
        for panel in mock_panels
    ]

    async with CAMSClient() as cams:
        # Get forecasts for the first location (in real implementation, would cover all locations)
        if panel_locations:
            first_location = panel_locations[0]
            forecasts = await cams.get_dust_forecast(first_location["lat"], first_location["lng"], 48)
            alerts = await cams.generate_dust_alerts(forecasts, panel_locations)

            return {
                "active_alerts": [alert.dict() for alert in alerts],
                "total_alerts": len(alerts),
                "panel_locations_monitored": len(panel_locations),
                "last_updated": datetime.now()
            }

    return {"active_alerts": [], "total_alerts": 0, "panel_locations_monitored": 0}

@app.get("/api/dust/visualization/{panel_id}")
async def get_dust_visualization_data(
    panel_id: str,
    days_back: int = 7,
    current_user: dict = Depends(get_current_active_user)
):
    """Get dust data visualization for specific panel"""
    # Find panel location
    panel = None
    for p in mock_panels:
        if p.panel_id == panel_id:
            panel = p
            break

    if not panel:
        raise HTTPException(status_code=404, detail="Panel not found")

    async with CAMSClient() as cams:
        # Get historical weather data
        weather_data = await cams.get_weather_data(panel.location.lat, panel.location.lng, days_back * 24)

        # Get forecast data
        forecasts = await cams.get_dust_forecast(panel.location.lat, panel.location.lng, 72)

        # Create visualization data
        historical_data = {
            "timestamps": [w.timestamp.isoformat() for w in weather_data],
            "dust_levels": [w.visibility for w in weather_data],  # Use visibility as proxy for dust
            "temperatures": [w.temperature for w in weather_data],
            "wind_speeds": [w.wind_speed for w in weather_data]
        }

        forecast_data = create_dust_visualization_data(forecasts)

        return {
            "panel_id": panel_id,
            "location": {"lat": panel.location.lat, "lng": panel.location.lng},
            "historical_data": historical_data,
            "forecast_data": forecast_data["chart_data"],
            "analysis": cams.analyze_dust_impact(forecasts),
            "generated_at": datetime.now()
        }

# Notification Endpoints

@app.post("/api/notifications/send")
async def send_notification(
    request: NotificationRequest,
    current_user: dict = Depends(require_permission("write:notifications"))
):
    """Send notification to recipients"""
    result = await notification_service.send_notification(request)
    logger.info(f"Notification sent by {current_user['username']}: {request.template_id}")
    return result

@app.get("/api/notifications/templates")
async def get_notification_templates(
    current_user: dict = Depends(get_current_active_user)
):
    """Get all notification templates"""
    return {
        "templates": [template.dict() for template in NOTIFICATION_TEMPLATES.values()],
        "total_templates": len(NOTIFICATION_TEMPLATES)
    }

@app.get("/api/notifications/recipients")
async def get_notification_recipients(
    current_user: dict = Depends(require_permission("read:users"))
):
    """Get all notification recipients"""
    return {
        "recipients": [recipient.dict() for recipient in NOTIFICATION_RECIPIENTS.values()],
        "total_recipients": len(NOTIFICATION_RECIPIENTS)
    }

@app.get("/api/notifications/logs")
async def get_notification_logs(
    limit: int = 100,
    current_user: dict = Depends(require_permission("read:notifications"))
):
    """Get notification logs"""
    logs = notification_service.get_notification_logs(limit)
    return {
        "logs": [log.dict() for log in logs],
        "total_logs": len(logs)
    }

@app.get("/api/notifications/stats")
async def get_notification_stats(
    current_user: dict = Depends(require_permission("read:notifications"))
):
    """Get notification statistics"""
    return notification_service.get_notification_stats()

@app.post("/api/notifications/test")
async def test_notification(
    template_id: str,
    recipient_id: str,
    admin_user: dict = Depends(get_admin_user)
):
    """Test notification sending (admin only)"""
    test_variables = {
        "location": "Test Solar Farm",
        "dust_level": "150",
        "efficiency_loss": "15",
        "recommendation": "Schedule cleaning within 24 hours",
        "affected_panels": "A01, A02, A03"
    }

    request = NotificationRequest(
        template_id=template_id,
        recipients=[recipient_id],
        variables=test_variables,
        priority="low"
    )

    result = await notification_service.send_notification(request)
    logger.info(f"Test notification sent by admin {admin_user['username']}")
    return result

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

