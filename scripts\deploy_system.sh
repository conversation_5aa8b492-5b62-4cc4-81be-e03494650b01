#!/bin/bash

# Solar AI Cleaning & Monitoring System - Deployment Script
# Comprehensive deployment automation for production environment

set -e  # Exit on any error

echo "🌞 Solar AI Cleaning & Monitoring System - Deployment Script"
echo "============================================================"

# Configuration
PROJECT_NAME="solar-ai-monitoring"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="./backups"
LOG_FILE="./deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if running as root (for production deployment)
    if [[ $EUID -eq 0 ]]; then
        warning "Running as root. Make sure this is intended for production deployment."
    fi
    
    log "Prerequisites check completed ✅"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "./logs"
    mkdir -p "./ssl"
    mkdir -p "./monitoring/prometheus"
    mkdir -p "./monitoring/grafana/dashboards"
    mkdir -p "./monitoring/grafana/datasources"
    mkdir -p "./mosquitto/config"
    mkdir -p "./mosquitto/data"
    mkdir -p "./mosquitto/log"
    
    log "Directories created ✅"
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certificates() {
    log "Generating SSL certificates..."
    
    if [ ! -f "./ssl/cert.pem" ]; then
        openssl req -x509 -newkey rsa:4096 -keyout ./ssl/key.pem -out ./ssl/cert.pem -days 365 -nodes \
            -subj "/C=SA/ST=Riyadh/L=Riyadh/O=Solar AI Monitoring/CN=localhost"
        log "SSL certificates generated ✅"
    else
        info "SSL certificates already exist, skipping generation"
    fi
}

# Create environment file
create_env_file() {
    log "Creating environment configuration..."
    
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# Solar AI Monitoring System Environment Configuration

# Database Configuration
POSTGRES_DB=solar_monitoring
POSTGRES_USER=solar_user
POSTGRES_PASSWORD=$(openssl rand -base64 32)

# Redis Configuration
REDIS_PASSWORD=$(openssl rand -base64 32)

# JWT Secret
SECRET_KEY=$(openssl rand -base64 64)

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=production

# SMTP Configuration (Update with your SMTP settings)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Twilio Configuration (Update with your Twilio credentials)
TWILIO_SID=your-twilio-sid
TWILIO_TOKEN=your-twilio-token
TWILIO_PHONE=+1234567890

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token

# CAMS API Configuration
CAMS_API_KEY=your-cams-api-key

# Monitoring Configuration
GRAFANA_ADMIN_PASSWORD=$(openssl rand -base64 32)

# Backup Configuration
BACKUP_RETENTION_DAYS=30
EOF
        log "Environment file created ✅"
        warning "Please update the .env file with your actual credentials before deployment"
    else
        info "Environment file already exists, skipping creation"
    fi
}

# Create monitoring configuration
create_monitoring_config() {
    log "Creating monitoring configuration..."
    
    # Prometheus configuration
    cat > ./monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'solar-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'solar-frontend'
    static_configs:
      - targets: ['frontend:3000']
    scrape_interval: 60s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 60s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 60s
EOF

    # Mosquitto configuration
    cat > ./mosquitto/config/mosquitto.conf << EOF
# Mosquitto MQTT Broker Configuration
listener 1883
allow_anonymous true
persistence true
persistence_location /mosquitto/data/
log_dest file /mosquitto/log/mosquitto.log
log_type error
log_type warning
log_type notice
log_type information
connection_messages true
log_timestamp true
EOF

    log "Monitoring configuration created ✅"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."
    
    BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
    
    mkdir -p "$BACKUP_PATH"
    
    # Backup database if exists
    if docker ps | grep -q "solar-postgres"; then
        docker exec solar-postgres pg_dump -U solar_user solar_monitoring > "$BACKUP_PATH/database.sql"
        log "Database backup created ✅"
    fi
    
    # Backup configuration files
    cp -r ./monitoring "$BACKUP_PATH/" 2>/dev/null || true
    cp .env "$BACKUP_PATH/" 2>/dev/null || true
    
    log "Backup completed: $BACKUP_PATH ✅"
}

# Deploy the system
deploy_system() {
    log "Deploying Solar AI Monitoring System..."
    
    # Pull latest images
    docker-compose pull
    
    # Build custom images
    docker-compose build --no-cache
    
    # Start services
    docker-compose up -d
    
    # Wait for services to be ready
    log "Waiting for services to start..."
    sleep 30
    
    # Check service health
    check_service_health
    
    log "System deployment completed ✅"
}

# Check service health
check_service_health() {
    log "Checking service health..."
    
    # Check backend health
    if curl -f http://localhost:8000/api/health > /dev/null 2>&1; then
        log "Backend service is healthy ✅"
    else
        error "Backend service is not responding"
    fi
    
    # Check frontend
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log "Frontend service is healthy ✅"
    else
        warning "Frontend service may not be ready yet"
    fi
    
    # Check database
    if docker exec solar-postgres pg_isready -U solar_user > /dev/null 2>&1; then
        log "Database service is healthy ✅"
    else
        error "Database service is not responding"
    fi
    
    # Check Redis
    if docker exec solar-redis redis-cli ping > /dev/null 2>&1; then
        log "Redis service is healthy ✅"
    else
        error "Redis service is not responding"
    fi
}

# Setup initial data
setup_initial_data() {
    log "Setting up initial data..."
    
    # Wait for backend to be fully ready
    sleep 10
    
    # Create initial admin user (if needed)
    # This would typically be done through the API or database migration
    
    log "Initial data setup completed ✅"
}

# Display deployment summary
display_summary() {
    log "Deployment Summary"
    echo "=================="
    echo ""
    echo "🌐 Frontend URL: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:8000"
    echo "📚 API Documentation: http://localhost:8000/docs"
    echo "📊 Grafana Dashboard: http://localhost:3001"
    echo "📈 Prometheus: http://localhost:9090"
    echo ""
    echo "🔐 Default Credentials:"
    echo "   Admin Username: admin"
    echo "   Admin Password: secret (change immediately)"
    echo ""
    echo "📁 Important Files:"
    echo "   Environment: .env"
    echo "   Logs: ./logs/"
    echo "   Backups: $BACKUP_DIR/"
    echo ""
    echo "🚀 System is ready for use!"
}

# Cleanup function
cleanup() {
    log "Cleaning up temporary files..."
    # Add cleanup logic here if needed
}

# Main deployment function
main() {
    log "Starting Solar AI Monitoring System deployment..."
    
    # Set trap for cleanup on exit
    trap cleanup EXIT
    
    # Run deployment steps
    check_prerequisites
    create_directories
    generate_ssl_certificates
    create_env_file
    create_monitoring_config
    backup_data
    deploy_system
    setup_initial_data
    display_summary
    
    log "Deployment completed successfully! 🎉"
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log "Stopping services..."
        docker-compose down
        log "Services stopped ✅"
        ;;
    "restart")
        log "Restarting services..."
        docker-compose restart
        log "Services restarted ✅"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    "backup")
        backup_data
        ;;
    "health")
        check_service_health
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status|backup|health}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Deploy the complete system (default)"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - Show service logs"
        echo "  status  - Show service status"
        echo "  backup  - Create data backup"
        echo "  health  - Check service health"
        exit 1
        ;;
esac
