/*
 * Solar Panel Vibration Monitoring System
 * 
 * This Arduino sketch reads vibration data from an MPU6050 sensor
 * and sends the data via MQTT for monitoring solar panel stability.
 * 
 * Hardware Requirements:
 * - Arduino (Uno, ESP32, or similar)
 * - MPU6050 accelerometer/gyroscope sensor
 * - WiFi module (if using Arduino Uno) or ESP32 with built-in WiFi
 * 
 * Libraries Required:
 * - MPU6050 library by Electronic Cats
 * - PubSubClient for MQTT
 * - WiFi library (ESP32) or WiFiEsp (Arduino Uno)
 * - ArduinoJson for JSON formatting
 * 
 * Wiring:
 * MPU6050 VCC -> 3.3V
 * MPU6050 GND -> GND
 * MPU6050 SCL -> A5 (Uno) or GPIO 22 (ESP32)
 * MPU6050 SDA -> A4 (Uno) or GPIO 21 (ESP32)
 */

#include <Wire.h>
#include <MPU6050.h>
#include <WiFi.h>  // Use WiFiEsp.h for Arduino Uno with WiFi module
#include <PubSubClient.h>
#include <ArduinoJson.h>

// WiFi credentials
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// MQTT broker settings
const char* mqtt_server = "YOUR_MQTT_BROKER_IP";
const int mqtt_port = 1883;
const char* mqtt_user = "YOUR_MQTT_USERNAME";
const char* mqtt_password = "YOUR_MQTT_PASSWORD";
const char* mqtt_topic = "solar/vibration";

// Device identification
const char* device_id = "solar_panel_A23";

// Initialize objects
MPU6050 mpu;
WiFiClient espClient;
PubSubClient client(espClient);

// Vibration thresholds
const float VIBRATION_THRESHOLD = 2.0;  // m/s²
const unsigned long READING_INTERVAL = 5000;  // 5 seconds
const unsigned long MQTT_RECONNECT_INTERVAL = 5000;  // 5 seconds

// Variables
unsigned long lastReading = 0;
unsigned long lastMqttAttempt = 0;
float baseline_ax = 0, baseline_ay = 0, baseline_az = 0;
bool calibrated = false;

void setup() {
  Serial.begin(115200);
  Wire.begin();
  
  // Initialize MPU6050
  Serial.println("Initializing MPU6050...");
  mpu.initialize();
  
  if (mpu.testConnection()) {
    Serial.println("MPU6050 connection successful");
  } else {
    Serial.println("MPU6050 connection failed");
    while(1);
  }
  
  // Connect to WiFi
  setupWiFi();
  
  // Setup MQTT
  client.setServer(mqtt_server, mqtt_port);
  client.setCallback(mqttCallback);
  
  // Calibrate sensor
  calibrateSensor();
  
  Serial.println("Setup complete. Starting vibration monitoring...");
}

void loop() {
  // Ensure MQTT connection
  if (!client.connected()) {
    reconnectMQTT();
  }
  client.loop();
  
  // Read vibration data at specified intervals
  if (millis() - lastReading >= READING_INTERVAL) {
    readAndSendVibrationData();
    lastReading = millis();
  }
  
  delay(100);
}

void setupWiFi() {
  delay(10);
  Serial.println();
  Serial.print("Connecting to ");
  Serial.println(ssid);
  
  WiFi.begin(ssid, password);
  
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  
  Serial.println("");
  Serial.println("WiFi connected");
  Serial.println("IP address: ");
  Serial.println(WiFi.localIP());
}

void reconnectMQTT() {
  if (millis() - lastMqttAttempt < MQTT_RECONNECT_INTERVAL) {
    return;
  }
  
  lastMqttAttempt = millis();
  
  Serial.print("Attempting MQTT connection...");
  
  if (client.connect(device_id, mqtt_user, mqtt_password)) {
    Serial.println("connected");
    // Subscribe to control topics if needed
    client.subscribe("solar/control");
  } else {
    Serial.print("failed, rc=");
    Serial.print(client.state());
    Serial.println(" try again in 5 seconds");
  }
}

void mqttCallback(char* topic, byte* payload, unsigned int length) {
  Serial.print("Message arrived [");
  Serial.print(topic);
  Serial.print("] ");
  
  String message;
  for (int i = 0; i < length; i++) {
    message += (char)payload[i];
  }
  Serial.println(message);
  
  // Handle control commands if needed
  if (String(topic) == "solar/control") {
    if (message == "calibrate") {
      calibrateSensor();
    }
  }
}

void calibrateSensor() {
  Serial.println("Calibrating sensor...");
  
  float sum_ax = 0, sum_ay = 0, sum_az = 0;
  int samples = 100;
  
  for (int i = 0; i < samples; i++) {
    int16_t ax, ay, az, gx, gy, gz;
    mpu.getMotion6(&ax, &ay, &az, &gx, &gy, &gz);
    
    sum_ax += ax;
    sum_ay += ay;
    sum_az += az;
    
    delay(10);
  }
  
  baseline_ax = sum_ax / samples;
  baseline_ay = sum_ay / samples;
  baseline_az = sum_az / samples;
  
  calibrated = true;
  Serial.println("Calibration complete");
}

void readAndSendVibrationData() {
  if (!calibrated) {
    return;
  }
  
  int16_t ax, ay, az, gx, gy, gz;
  mpu.getMotion6(&ax, &ay, &az, &gx, &gy, &gz);
  
  // Convert to g-force (assuming ±2g range)
  float accel_x = (ax - baseline_ax) / 16384.0;
  float accel_y = (ay - baseline_ay) / 16384.0;
  float accel_z = (az - baseline_az) / 16384.0;
  
  // Convert to m/s²
  float vibration_x = accel_x * 9.81;
  float vibration_y = accel_y * 9.81;
  float vibration_z = accel_z * 9.81;
  
  // Calculate magnitude
  float vibration_magnitude = sqrt(vibration_x*vibration_x + 
                                  vibration_y*vibration_y + 
                                  vibration_z*vibration_z);
  
  // Create JSON payload
  StaticJsonDocument<300> doc;
  doc["device_id"] = device_id;
  doc["timestamp"] = millis();
  doc["vibration"]["x"] = vibration_x;
  doc["vibration"]["y"] = vibration_y;
  doc["vibration"]["z"] = vibration_z;
  doc["vibration"]["magnitude"] = vibration_magnitude;
  doc["alert"] = vibration_magnitude > VIBRATION_THRESHOLD;
  
  char buffer[300];
  serializeJson(doc, buffer);
  
  // Send via MQTT
  if (client.connected()) {
    client.publish(mqtt_topic, buffer);
    Serial.print("Vibration data sent: ");
    Serial.println(buffer);
    
    // Print alert if threshold exceeded
    if (vibration_magnitude > VIBRATION_THRESHOLD) {
      Serial.println("WARNING: Vibration threshold exceeded!");
    }
  } else {
    Serial.println("MQTT not connected, data not sent");
  }
}

