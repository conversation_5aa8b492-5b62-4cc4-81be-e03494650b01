import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON>, But<PERSON>, Badge } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { LineChart } from 'react-native-chart-kit';
import { AppState, PanelStatus, SystemStats } from '../types';
import { fetchPanelStatus, fetchSystemStats } from '../store/panelsSlice';
import { fetchCurrentDustData } from '../store/dustDataSlice';
import { PanelCard } from '../components/PanelCard';
import { StatCard } from '../components/StatCard';

const DashboardScreen: React.FC = ({ navigation }: any) => {
  const dispatch = useDispatch();
  const { panels, stats, loading } = useSelector((state: AppState) => state.panels);
  const { current: currentDustData } = useSelector((state: AppState) => state.dustData);
  const { unreadCount } = useSelector((state: AppState) => state.notifications);
  
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        dispatch(fetchPanelStatus()),
        dispatch(fetchSystemStats()),
        dispatch(fetchCurrentDustData()),
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to load dashboard data');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleScheduleCleaning = () => {
    const panelsNeedingCleaning = panels.filter(
      panel => panel.status === 'dusty' || panel.status === 'needs_attention'
    );
    
    if (panelsNeedingCleaning.length === 0) {
      Alert.alert('Info', 'No panels currently need cleaning');
      return;
    }

    Alert.alert(
      'Schedule Cleaning',
      `Schedule cleaning for ${panelsNeedingCleaning.length} panels?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Schedule', 
          onPress: () => {
            // Implement cleaning scheduling logic
            Alert.alert('Success', 'Cleaning scheduled successfully');
          }
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'clean': return '#27ae60';
      case 'dusty': return '#f39c12';
      case 'needs_attention': return '#e74c3c';
      case 'offline': return '#95a5a6';
      default: return '#95a5a6';
    }
  };

  const chartData = {
    labels: ['6h', '12h', '18h', '24h', 'Now'],
    datasets: [
      {
        data: [15, 22, 28, 35, currentDustData?.dust_level || 25],
        color: (opacity = 1) => `rgba(244, 156, 18, ${opacity})`,
        strokeWidth: 3,
      },
    ],
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <LinearGradient
        colors={['#3498db', '#2980b9']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>Solar AI Monitor</Text>
            <Text style={styles.headerSubtitle}>
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </Text>
          </View>
          <TouchableOpacity 
            style={styles.notificationButton}
            onPress={() => navigation.navigate('Notifications')}
          >
            <Icon name="notifications" size={24} color="#fff" />
            {unreadCount > 0 && (
              <Badge
                value={unreadCount}
                status="error"
                containerStyle={styles.badgeContainer}
              />
            )}
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* System Overview Cards */}
      <View style={styles.statsContainer}>
        <StatCard
          title="Total Panels"
          value={stats?.total_panels || 0}
          icon="solar-panel"
          color="#3498db"
        />
        <StatCard
          title="Active"
          value={stats?.active_panels || 0}
          icon="check-circle"
          color="#27ae60"
        />
        <StatCard
          title="Need Cleaning"
          value={stats?.panels_needing_cleaning || 0}
          icon="warning"
          color="#f39c12"
        />
        <StatCard
          title="Efficiency"
          value={`${stats?.average_efficiency || 0}%`}
          icon="trending-up"
          color="#9b59b6"
        />
      </View>

      {/* Dust Level Chart */}
      <Card containerStyle={styles.chartCard}>
        <Text style={styles.chartTitle}>Dust Level Trend (24h)</Text>
        <LineChart
          data={chartData}
          width={320}
          height={200}
          chartConfig={{
            backgroundColor: '#ffffff',
            backgroundGradientFrom: '#ffffff',
            backgroundGradientTo: '#ffffff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(52, 152, 219, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 16,
            },
            propsForDots: {
              r: '6',
              strokeWidth: '2',
              stroke: '#f39c12',
            },
          }}
          bezier
          style={styles.chart}
        />
      </Card>

      {/* Quick Actions */}
      <Card containerStyle={styles.actionsCard}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionsContainer}>
          <Button
            title="Schedule Cleaning"
            icon={<Icon name="cleaning-services" size={20} color="#fff" />}
            buttonStyle={[styles.actionButton, { backgroundColor: '#27ae60' }]}
            onPress={handleScheduleCleaning}
          />
          <Button
            title="View Reports"
            icon={<Icon name="assessment" size={20} color="#fff" />}
            buttonStyle={[styles.actionButton, { backgroundColor: '#3498db' }]}
            onPress={() => navigation.navigate('Reports')}
          />
        </View>
      </Card>

      {/* Panel Status Grid */}
      <Card containerStyle={styles.panelsCard}>
        <View style={styles.panelsHeader}>
          <Text style={styles.sectionTitle}>Panel Status</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Panels')}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.panelsGrid}>
          {panels.slice(0, 6).map((panel) => (
            <PanelCard
              key={panel.panel_id}
              panel={panel}
              onPress={(panel) => 
                navigation.navigate('PanelDetails', { panelId: panel.panel_id })
              }
            />
          ))}
        </View>
      </Card>

      {/* Current Conditions */}
      {currentDustData && (
        <Card containerStyle={styles.conditionsCard}>
          <Text style={styles.sectionTitle}>Current Conditions</Text>
          <View style={styles.conditionsGrid}>
            <View style={styles.conditionItem}>
              <Icon name="grain" size={24} color="#f39c12" />
              <Text style={styles.conditionLabel}>Dust Level</Text>
              <Text style={styles.conditionValue}>{currentDustData.dust_level}%</Text>
            </View>
            <View style={styles.conditionItem}>
              <Icon name="thermostat" size={24} color="#e74c3c" />
              <Text style={styles.conditionLabel}>Temperature</Text>
              <Text style={styles.conditionValue}>
                {currentDustData.weather_conditions.temperature}°C
              </Text>
            </View>
            <View style={styles.conditionItem}>
              <Icon name="air" size={24} color="#3498db" />
              <Text style={styles.conditionLabel}>Wind Speed</Text>
              <Text style={styles.conditionValue}>
                {currentDustData.weather_conditions.wind_speed} km/h
              </Text>
            </View>
            <View style={styles.conditionItem}>
              <Icon name="opacity" size={24} color="#1abc9c" />
              <Text style={styles.conditionLabel}>Humidity</Text>
              <Text style={styles.conditionValue}>
                {currentDustData.weather_conditions.humidity}%
              </Text>
            </View>
          </View>
        </Card>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#ecf0f1',
    marginTop: 4,
  },
  notificationButton: {
    position: 'relative',
  },
  badgeContainer: {
    position: 'absolute',
    top: -8,
    right: -8,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    marginTop: -10,
  },
  chartCard: {
    margin: 15,
    borderRadius: 10,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#2c3e50',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  actionsCard: {
    margin: 15,
    borderRadius: 10,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#2c3e50',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 140,
  },
  panelsCard: {
    margin: 15,
    borderRadius: 10,
    elevation: 3,
  },
  panelsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  viewAllText: {
    color: '#3498db',
    fontSize: 16,
    fontWeight: '600',
  },
  panelsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  conditionsCard: {
    margin: 15,
    borderRadius: 10,
    elevation: 3,
    marginBottom: 30,
  },
  conditionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  conditionItem: {
    alignItems: 'center',
    width: '48%',
    marginBottom: 15,
  },
  conditionLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 5,
  },
  conditionValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 2,
  },
});

export default DashboardScreen;
