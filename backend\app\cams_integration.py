#!/usr/bin/env python3
"""
CAMS (Copernicus Atmosphere Monitoring Service) Integration
Provides dust forecasting and atmospheric data for solar panel monitoring
"""

import asyncio
import httpx
import pandas as pd
import xarray as xr
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pydantic import BaseModel
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class DustForecast(BaseModel):
    timestamp: datetime
    location: Dict[str, float]  # lat, lng
    dust_concentration: float  # μg/m³
    visibility: float  # km
    wind_speed: float  # m/s
    wind_direction: float  # degrees
    temperature: float  # °C
    humidity: float  # %
    pressure: float  # hPa
    forecast_confidence: float  # 0-1

class WeatherData(BaseModel):
    timestamp: datetime
    location: Dict[str, float]
    temperature: float
    humidity: float
    wind_speed: float
    wind_direction: float
    precipitation: float
    cloud_cover: float
    uv_index: float
    visibility: float

class DustAlert(BaseModel):
    alert_id: str
    timestamp: datetime
    location: Dict[str, float]
    severity: str  # low, medium, high, extreme
    dust_level: float
    expected_duration: int  # hours
    cleaning_recommendation: str
    affected_panels: List[str]

class CAMSClient:
    """Client for CAMS atmospheric data"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("CAMS_API_KEY")
        self.base_url = "https://ads.atmosphere.copernicus.eu/api/v2"
        self.session = None
        
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=30.0)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()

    async def get_dust_forecast(
        self, 
        lat: float, 
        lng: float, 
        hours_ahead: int = 72
    ) -> List[DustForecast]:
        """Get dust concentration forecast for location"""
        
        try:
            # CAMS dust forecast parameters
            params = {
                'variable': ['dust_aerosol_optical_depth_550nm', 'total_aerosol_optical_depth_550nm'],
                'date': f"{datetime.now().strftime('%Y-%m-%d')}/{(datetime.now() + timedelta(hours=hours_ahead)).strftime('%Y-%m-%d')}",
                'time': [f"{i:02d}:00" for i in range(0, 24, 3)],  # Every 3 hours
                'area': [lat + 0.5, lng - 0.5, lat - 0.5, lng + 0.5],  # Bounding box
                'format': 'netcdf',
                'grid': '0.4/0.4'  # Resolution
            }
            
            # Simulate CAMS API response (replace with actual API call)
            forecasts = []
            base_time = datetime.now()
            
            for hour in range(0, hours_ahead, 3):
                forecast_time = base_time + timedelta(hours=hour)
                
                # Simulate dust concentration with realistic patterns
                base_dust = 50.0  # Base dust level
                seasonal_factor = 1.5 if forecast_time.month in [3, 4, 5, 6] else 1.0  # Spring/early summer
                daily_cycle = 1.2 if 10 <= forecast_time.hour <= 16 else 0.8  # Higher during day
                random_variation = np.random.normal(1.0, 0.2)
                
                dust_concentration = base_dust * seasonal_factor * daily_cycle * random_variation
                dust_concentration = max(10.0, min(500.0, dust_concentration))  # Realistic bounds
                
                # Calculate visibility based on dust
                visibility = max(1.0, 50.0 - (dust_concentration / 10.0))
                
                # Simulate weather data
                wind_speed = np.random.uniform(2.0, 15.0)
                wind_direction = np.random.uniform(0, 360)
                temperature = 25.0 + np.random.normal(0, 5)
                humidity = max(10, min(90, 40 + np.random.normal(0, 15)))
                pressure = 1013.25 + np.random.normal(0, 10)
                
                # Confidence based on forecast distance
                confidence = max(0.5, 1.0 - (hour / hours_ahead) * 0.4)
                
                forecast = DustForecast(
                    timestamp=forecast_time,
                    location={"lat": lat, "lng": lng},
                    dust_concentration=dust_concentration,
                    visibility=visibility,
                    wind_speed=wind_speed,
                    wind_direction=wind_direction,
                    temperature=temperature,
                    humidity=humidity,
                    pressure=pressure,
                    forecast_confidence=confidence
                )
                
                forecasts.append(forecast)
            
            logger.info(f"Retrieved {len(forecasts)} dust forecasts for location ({lat}, {lng})")
            return forecasts
            
        except Exception as e:
            logger.error(f"Error fetching dust forecast: {e}")
            return []

    async def get_weather_data(
        self, 
        lat: float, 
        lng: float, 
        hours_back: int = 24
    ) -> List[WeatherData]:
        """Get historical weather data"""
        
        try:
            weather_data = []
            base_time = datetime.now()
            
            for hour in range(-hours_back, 1):
                data_time = base_time + timedelta(hours=hour)
                
                # Simulate realistic weather patterns
                temp_base = 25.0
                temp_daily_cycle = 8.0 * np.sin((data_time.hour - 6) * np.pi / 12)
                temperature = temp_base + temp_daily_cycle + np.random.normal(0, 2)
                
                humidity = max(20, min(90, 50 + np.random.normal(0, 15)))
                wind_speed = max(0, np.random.exponential(5))
                wind_direction = np.random.uniform(0, 360)
                precipitation = max(0, np.random.exponential(0.1) if np.random.random() < 0.1 else 0)
                cloud_cover = max(0, min(100, np.random.normal(30, 20)))
                uv_index = max(0, min(11, 8 * np.sin(max(0, (data_time.hour - 6) * np.pi / 12))))
                visibility = max(5, 50 - np.random.exponential(5))
                
                weather = WeatherData(
                    timestamp=data_time,
                    location={"lat": lat, "lng": lng},
                    temperature=temperature,
                    humidity=humidity,
                    wind_speed=wind_speed,
                    wind_direction=wind_direction,
                    precipitation=precipitation,
                    cloud_cover=cloud_cover,
                    uv_index=uv_index,
                    visibility=visibility
                )
                
                weather_data.append(weather)
            
            logger.info(f"Retrieved {len(weather_data)} weather records for location ({lat}, {lng})")
            return weather_data
            
        except Exception as e:
            logger.error(f"Error fetching weather data: {e}")
            return []

    def analyze_dust_impact(self, forecasts: List[DustForecast]) -> Dict[str, any]:
        """Analyze dust impact on solar panels"""
        
        if not forecasts:
            return {"error": "No forecast data available"}
        
        # Calculate metrics
        avg_dust = np.mean([f.dust_concentration for f in forecasts])
        max_dust = max([f.dust_concentration for f in forecasts])
        dust_trend = "increasing" if forecasts[-1].dust_concentration > forecasts[0].dust_concentration else "decreasing"
        
        # Efficiency impact estimation
        efficiency_loss = min(35.0, avg_dust * 0.1)  # Rough correlation
        
        # Cleaning recommendations
        cleaning_urgency = "low"
        if avg_dust > 200:
            cleaning_urgency = "high"
        elif avg_dust > 100:
            cleaning_urgency = "medium"
        
        # Optimal cleaning window
        optimal_hours = []
        for i, forecast in enumerate(forecasts):
            if (forecast.dust_concentration < avg_dust * 0.8 and 
                forecast.wind_speed < 10 and 
                forecast.humidity < 70):
                optimal_hours.append(i * 3)  # Convert to hours
        
        return {
            "analysis_timestamp": datetime.now(),
            "forecast_period_hours": len(forecasts) * 3,
            "average_dust_concentration": round(avg_dust, 2),
            "maximum_dust_concentration": round(max_dust, 2),
            "dust_trend": dust_trend,
            "estimated_efficiency_loss": round(efficiency_loss, 2),
            "cleaning_urgency": cleaning_urgency,
            "optimal_cleaning_hours": optimal_hours[:5],  # Top 5 windows
            "recommendations": self._generate_recommendations(avg_dust, max_dust, forecasts)
        }

    def _generate_recommendations(
        self, 
        avg_dust: float, 
        max_dust: float, 
        forecasts: List[DustForecast]
    ) -> List[str]:
        """Generate cleaning recommendations based on dust analysis"""
        
        recommendations = []
        
        if max_dust > 300:
            recommendations.append("URGENT: Extremely high dust levels detected. Immediate cleaning recommended.")
        elif max_dust > 200:
            recommendations.append("HIGH: Significant dust accumulation expected. Schedule cleaning within 24 hours.")
        elif max_dust > 100:
            recommendations.append("MEDIUM: Moderate dust levels. Consider cleaning within 48-72 hours.")
        else:
            recommendations.append("LOW: Dust levels manageable. Regular maintenance schedule sufficient.")
        
        # Wind-based recommendations
        high_wind_periods = [f for f in forecasts if f.wind_speed > 12]
        if high_wind_periods:
            recommendations.append("Avoid cleaning during high wind periods to prevent recontamination.")
        
        # Humidity recommendations
        high_humidity_periods = [f for f in forecasts if f.humidity > 80]
        if high_humidity_periods:
            recommendations.append("High humidity periods may reduce cleaning effectiveness.")
        
        # Optimal timing
        low_dust_periods = [f for f in forecasts if f.dust_concentration < avg_dust * 0.7]
        if low_dust_periods:
            recommendations.append(f"Optimal cleaning window: {low_dust_periods[0].timestamp.strftime('%Y-%m-%d %H:%M')}")
        
        return recommendations

    async def generate_dust_alerts(
        self, 
        forecasts: List[DustForecast], 
        panel_locations: List[Dict[str, any]]
    ) -> List[DustAlert]:
        """Generate dust alerts for panel locations"""
        
        alerts = []
        
        for forecast in forecasts:
            if forecast.dust_concentration > 200:  # High dust threshold
                severity = "extreme" if forecast.dust_concentration > 400 else "high"
                
                # Find affected panels (within 5km radius)
                affected_panels = []
                for panel in panel_locations:
                    distance = self._calculate_distance(
                        forecast.location["lat"], forecast.location["lng"],
                        panel["lat"], panel["lng"]
                    )
                    if distance <= 5.0:  # 5km radius
                        affected_panels.append(panel["panel_id"])
                
                if affected_panels:
                    alert = DustAlert(
                        alert_id=f"dust_{forecast.timestamp.strftime('%Y%m%d_%H%M')}",
                        timestamp=forecast.timestamp,
                        location=forecast.location,
                        severity=severity,
                        dust_level=forecast.dust_concentration,
                        expected_duration=6,  # Estimated duration in hours
                        cleaning_recommendation=self._get_cleaning_method_recommendation(forecast.dust_concentration),
                        affected_panels=affected_panels
                    )
                    alerts.append(alert)
        
        return alerts

    def _calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance between two points in kilometers"""
        from math import radians, sin, cos, sqrt, atan2
        
        R = 6371  # Earth's radius in kilometers
        
        lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlng/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        return R * c

    def _get_cleaning_method_recommendation(self, dust_level: float) -> str:
        """Recommend cleaning method based on dust level"""
        if dust_level > 400:
            return "crawler_robots"  # Most thorough for extreme dust
        elif dust_level > 200:
            return "drone_waterless"  # Efficient for high dust
        elif dust_level > 100:
            return "air_blowers"  # Quick for moderate dust
        else:
            return "ultrasonic_vibrations"  # Gentle for light dust

# Utility functions for data processing
def process_cams_netcdf(file_path: str) -> pd.DataFrame:
    """Process CAMS NetCDF data file"""
    try:
        ds = xr.open_dataset(file_path)
        df = ds.to_dataframe().reset_index()
        return df
    except Exception as e:
        logger.error(f"Error processing NetCDF file: {e}")
        return pd.DataFrame()

def create_dust_visualization_data(forecasts: List[DustForecast]) -> Dict[str, any]:
    """Create data structure for frontend visualization"""
    
    timestamps = [f.timestamp.isoformat() for f in forecasts]
    dust_levels = [f.dust_concentration for f in forecasts]
    visibility = [f.visibility for f in forecasts]
    wind_speeds = [f.wind_speed for f in forecasts]
    
    return {
        "chart_data": {
            "timestamps": timestamps,
            "dust_concentration": dust_levels,
            "visibility": visibility,
            "wind_speed": wind_speeds
        },
        "summary": {
            "total_forecasts": len(forecasts),
            "avg_dust": round(np.mean(dust_levels), 2),
            "max_dust": round(max(dust_levels), 2),
            "min_dust": round(min(dust_levels), 2)
        }
    }
