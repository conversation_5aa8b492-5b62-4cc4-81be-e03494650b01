<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام مراقبة وتنظيف الألواح الشمسية بالذكاء الاصطناعي</title>
    
    <!-- Reveal.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/theme/white.css">
    
    <!-- Arabic Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS for Arabic RTL -->
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #059669;
            --accent-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --gold-color: #fbbf24;
            --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            --gradient-secondary: linear-gradient(135deg, #059669 0%, #10b981 100%);
            --gradient-accent: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
        }

        * {
            box-sizing: border-box;
        }

        .reveal {
            font-family: 'Tajawal', 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
        }

        .reveal h1, .reveal h2, .reveal h3, .reveal h4, .reveal h5, .reveal h6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            color: var(--dark-color);
            font-weight: 700;
            text-align: center;
            margin-bottom: 1em;
        }

        .reveal h1 {
            font-size: 2.5em;
            font-weight: 900;
        }

        .reveal h2 {
            font-size: 2em;
            font-weight: 700;
        }

        .reveal h3 {
            font-size: 1.5em;
            font-weight: 600;
        }

        .reveal .slides section {
            text-align: right;
            direction: rtl;
        }

        .reveal .slides section.center {
            text-align: center;
        }

        /* Title Slide */
        .title-slide {
            text-align: center !important;
            background: var(--gradient-primary);
            color: white !important;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .title-slide h1, .title-slide h2, .title-slide p {
            color: white !important;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .title-slide h1 {
            font-size: 3em;
            margin-bottom: 0.5em;
            background: linear-gradient(45deg, #ffffff, #fbbf24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title-slide h2 {
            font-size: 1.8em;
            margin-bottom: 1em;
            opacity: 0.95;
        }

        /* Stats Container */
        .stats-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 25px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            min-width: 160px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .stat-number {
            font-size: 3em;
            font-weight: 900;
            color: var(--primary-color);
            margin-bottom: 0.2em;
            font-family: 'Cairo', sans-serif;
        }

        .stat-label {
            color: var(--dark-color);
            font-weight: 600;
            font-size: 1.1em;
        }

        /* Method Grid */
        .method-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .method-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border-right: 5px solid var(--primary-color);
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .method-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 3px;
            background: var(--gradient-primary);
        }

        .method-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-right-color: var(--accent-color);
        }

        .method-card.new {
            border-right-color: var(--accent-color);
            background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
        }

        .method-card.new::before {
            background: var(--gradient-accent);
        }

        .method-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            text-align: center;
        }

        .method-card h3 {
            color: var(--dark-color);
            margin-bottom: 10px;
            font-size: 1.3em;
            text-align: center;
        }

        .method-card p {
            color: #6b7280;
            margin-bottom: 15px;
            line-height: 1.6;
            text-align: center;
        }

        .method-card small {
            color: var(--primary-color);
            font-weight: 600;
            display: block;
            text-align: center;
            background: #f1f5f9;
            padding: 8px;
            border-radius: 8px;
        }

        /* Comparison Table */
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            direction: rtl;
        }

        .comparison-table th {
            background: var(--gradient-primary);
            color: white;
            padding: 18px 15px;
            font-weight: 700;
            text-align: center;
            font-size: 1.1em;
        }

        .comparison-table td {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
            text-align: center;
            font-weight: 500;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8fafc;
        }

        .comparison-table tr:hover {
            background: #e0f2fe;
        }

        .highlight-green { 
            color: var(--secondary-color); 
            font-weight: 700;
            background: #dcfce7;
            padding: 5px 10px;
            border-radius: 6px;
        }

        .highlight-red { 
            color: var(--danger-color); 
            font-weight: 700;
            background: #fee2e2;
            padding: 5px 10px;
            border-radius: 6px;
        }

        .highlight-blue { 
            color: var(--primary-color); 
            font-weight: 700;
            background: #dbeafe;
            padding: 5px 10px;
            border-radius: 6px;
        }

        /* ROI Highlight */
        .roi-highlight {
            background: var(--gradient-secondary);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 25px 0;
            box-shadow: 0 10px 30px rgba(5, 150, 105, 0.3);
        }

        .roi-highlight h3 {
            font-size: 2.2em;
            margin-bottom: 10px;
            color: white;
        }

        /* Sensor Widgets */
        .sensor-widget {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
            min-width: 140px;
        }

        .sensor-widget:hover {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .sensor-value {
            font-size: 2em;
            font-weight: 900;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-family: 'Cairo', sans-serif;
        }

        /* Chart Container */
        .chart-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* Gantt Container */
        .gantt-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .gantt-bar {
            height: 40px;
            margin: 15px 0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-weight: 700;
            font-size: 1.1em;
            transition: transform 0.3s ease;
        }

        .gantt-bar:hover {
            transform: scale(1.02);
        }

        .phase1 { background: var(--gradient-primary); }
        .phase2 { background: var(--gradient-secondary); }
        .phase3 { background: var(--gradient-accent); }
        .phase4 { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); }

        /* Lists */
        .reveal ul {
            list-style: none;
            padding-right: 0;
        }

        .reveal ul li {
            position: relative;
            padding-right: 30px;
            margin-bottom: 15px;
            line-height: 1.6;
            font-size: 1.1em;
        }

        .reveal ul li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: var(--secondary-color);
            font-weight: bold;
            font-size: 1.2em;
        }

        /* Progress and Controls */
        .reveal .progress {
            color: var(--primary-color);
        }

        .reveal .controls {
            color: var(--primary-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .stats-container {
                flex-direction: column;
            }
            
            .method-grid {
                grid-template-columns: 1fr;
            }
            
            .title-slide h1 {
                font-size: 2em;
            }
            
            .stat-number {
                font-size: 2.5em;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.8s ease-out;
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* New Badge */
        .new-badge {
            background: var(--gradient-accent);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 700;
            margin-right: 10px;
            display: inline-block;
        }

        /* Contact Info */
        .contact-info {
            background: var(--light-color);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid var(--primary-color);
        }

        .contact-info h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .contact-info p {
            margin: 8px 0;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="reveal">
        <div class="slides">
            
            <!-- الشريحة الرئيسية -->
            <section class="title-slide center">
                <h1 class="fade-in">نظام مراقبة وتنظيف الألواح الشمسية</h1>
                <h2 class="fade-in">المدعوم بالذكاء الاصطناعي</h2>
                <p style="font-size: 1.3em; margin-bottom: 30px;">ثورة في صيانة الألواح الشمسية بتقنيات متطورة</p>
                <div class="stats-container">
                    <div class="stat-item slide-in-right">
                        <div class="stat-number">9</div>
                        <div class="stat-label">طرق التنظيف</div>
                    </div>
                    <div class="stat-item slide-in-right">
                        <div class="stat-number">99%</div>
                        <div class="stat-label">دقة الذكاء الاصطناعي</div>
                    </div>
                    <div class="stat-item slide-in-right">
                        <div class="stat-number">50%</div>
                        <div class="stat-label">توفير التكاليف</div>
                    </div>
                </div>
            </section>

            <!-- التحديات الحالية -->
            <section>
                <h2>التحديات الحالية في صيانة الألواح الشمسية</h2>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">35%</div>
                        <div class="stat-label">فقدان الكفاءة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">7.5 مليون ريال</div>
                        <div class="stat-label">خسائر سنوية</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">70%</div>
                        <div class="stat-label">هدر المياه</div>
                    </div>
                </div>

                <ul>
                    <li>تراكم الغبار يقلل كفاءة الألواح الشمسية بنسبة تصل إلى 35%</li>
                    <li>طرق التنظيف التقليدية مكلفة وتستهلك كميات كبيرة من المياه</li>
                    <li>الفحص اليدوي يفوت العيوب الحرجة والشقوق الدقيقة</li>
                    <li>الصيانة التفاعلية تؤدي إلى توقف طويل للنظام</li>
                    <li>خيارات تنظيف محدودة للظروف البيئية المختلفة</li>
                </ul>
            </section>

            <!-- الحل المبتكر -->
            <section>
                <h2>الحل المبتكر: 9 طرق تنظيف متطورة</h2>
                <div class="method-grid">
                    <div class="method-card">
                        <div class="method-icon">🚁</div>
                        <h3>التنظيف بالطائرات المائية</h3>
                        <p>تنظيف تقليدي بالطائرات بدون طيار والمياه</p>
                        <small>الكفاءة: 85% | استهلاك المياه: 0.7 لتر/م²</small>
                    </div>

                    <div class="method-card">
                        <div class="method-icon">⚡</div>
                        <h3>التنظيف بالطائرات الجافة</h3>
                        <p>تقنية متطورة للتنظيف بدون مياه</p>
                        <small>الكفاءة: 90% | استهلاك المياه: 0.0 لتر/م²</small>
                    </div>

                    <div class="method-card">
                        <div class="method-icon">🤖</div>
                        <h3>الروبوتات الزاحفة</h3>
                        <p>روبوتات ذاتية التحكم تتحرك على الألواح</p>
                        <small>الكفاءة: 95% | استهلاك المياه: 0.1 لتر/م²</small>
                    </div>

                    <div class="method-card">
                        <div class="method-icon">✨</div>
                        <h3>الطلاءات النانوية</h3>
                        <p>طلاءات ذاتية التنظيف مقاومة للماء</p>
                        <small>الكفاءة: 75% | استهلاك المياه: 0.0 لتر/م²</small>
                    </div>

                    <div class="method-card">
                        <div class="method-icon">💨</div>
                        <h3>المنافخ الهوائية</h3>
                        <p>نظام هواء مضغوط عالي الضغط</p>
                        <small>الكفاءة: 70% | استهلاك المياه: 0.0 لتر/م²</small>
                    </div>

                    <div class="method-card">
                        <div class="method-icon">🌊</div>
                        <h3>الاهتزازات فوق الصوتية</h3>
                        <p>إزالة الغبار بالترددات الصوتية</p>
                        <small>الكفاءة: 80% | استهلاك المياه: 0.0 لتر/م²</small>
                    </div>

                    <div class="method-card new">
                        <div class="method-icon">⚡</div>
                        <h3><span class="new-badge">جديد</span>التنظيف الكهروستاتيكي</h3>
                        <p>تقنية الشحن الكهروستاتيكي لطرد الغبار</p>
                        <small>الكفاءة: 85% | استهلاك المياه: 0.0 لتر/م²</small>
                    </div>

                    <div class="method-card new">
                        <div class="method-icon">☀️</div>
                        <h3><span class="new-badge">جديد</span>التنظيف بالأشعة فوق البنفسجية</h3>
                        <p>معالجة التلوث العضوي بالأشعة فوق البنفسجية</p>
                        <small>الكفاءة: 65% | استهلاك المياه: 0.0 لتر/م²</small>
                    </div>

                    <div class="method-card new">
                        <div class="method-icon">🧠</div>
                        <h3><span class="new-badge">جديد</span>الصيانة التنبؤية</h3>
                        <p>جدولة ذكية مدعومة بالذكاء الاصطناعي</p>
                        <small>تحسين جميع الطرق حسب الظروف</small>
                    </div>
                </div>
            </section>

            <!-- مقارنة طرق التنظيف -->
            <section>
                <h2>مقارنة طرق التنظيف المختلفة</h2>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>الطريقة</th>
                            <th>الكفاءة</th>
                            <th>استهلاك المياه</th>
                            <th>التكلفة/ميجاوات</th>
                            <th>المدة الزمنية</th>
                            <th>تأثر بالطقس</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>الروبوتات الزاحفة</td>
                            <td class="highlight-green">95%</td>
                            <td class="highlight-green">0.1 لتر/م²</td>
                            <td class="highlight-green">300 ريال</td>
                            <td>60 دقيقة</td>
                            <td class="highlight-green">لا</td>
                        </tr>
                        <tr>
                            <td>الطائرات الجافة</td>
                            <td class="highlight-green">90%</td>
                            <td class="highlight-green">0.0 لتر/م²</td>
                            <td>450 ريال</td>
                            <td class="highlight-green">30 دقيقة</td>
                            <td class="highlight-green">لا</td>
                        </tr>
                        <tr>
                            <td>الكهروستاتيكي</td>
                            <td class="highlight-blue">85%</td>
                            <td class="highlight-green">0.0 لتر/م²</td>
                            <td class="highlight-green">340 ريال</td>
                            <td class="highlight-green">10 دقائق</td>
                            <td class="highlight-green">لا</td>
                        </tr>
                        <tr>
                            <td>الطائرات المائية</td>
                            <td class="highlight-blue">85%</td>
                            <td class="highlight-red">0.7 لتر/م²</td>
                            <td>560 ريال</td>
                            <td>45 دقيقة</td>
                            <td class="highlight-red">نعم</td>
                        </tr>
                        <tr>
                            <td>فوق الصوتية</td>
                            <td class="highlight-blue">80%</td>
                            <td class="highlight-green">0.0 لتر/م²</td>
                            <td>375 ريال</td>
                            <td class="highlight-green">15 دقيقة</td>
                            <td class="highlight-green">لا</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- تكامل أجهزة الاستشعار -->
            <section>
                <h2>تكامل أجهزة الاستشعار الذكية</h2>
                <div class="stats-container">
                    <div class="sensor-widget">
                        <div class="sensor-value">42.3°م</div>
                        <div>درجة الحرارة</div>
                    </div>
                    <div class="sensor-widget">
                        <div class="sensor-value">23%</div>
                        <div>الرطوبة</div>
                    </div>
                    <div class="sensor-widget">
                        <div class="sensor-value">12.4 فولت</div>
                        <div>الجهد الكهربائي</div>
                    </div>
                    <div class="sensor-widget">
                        <div class="sensor-value">850 واط</div>
                        <div>الطاقة المنتجة</div>
                    </div>
                </div>

                <h3>ميزات المراقبة في الوقت الفعلي:</h3>
                <ul>
                    <li><strong>أجهزة استشعار الحرارة:</strong> مراقبة حرارة الألواح والكفاءة</li>
                    <li><strong>أجهزة استشعار الرطوبة:</strong> التنبؤ بالتكثف واحتياجات التنظيف</li>
                    <li><strong>مراقبة الجهد والتيار:</strong> مراقبة الأداء في الوقت الفعلي</li>
                    <li><strong>أجهزة استشعار الاهتزاز:</strong> اكتشاف المشاكل الميكانيكية</li>
                    <li><strong>كثافة الغبار:</strong> مراقبة مستوى الغبار البيئي</li>
                    <li><strong>تكامل الطقس:</strong> بيانات الغلاف الجوي من CAMS</li>
                </ul>
            </section>

            <!-- تحليل العائد على الاستثمار -->
            <section>
                <h2>تحليل العائد على الاستثمار المحسن</h2>
                <div class="roi-highlight">
                    <h3>عائد 5 سنوات: 30.8 مليون ريال صافي</h3>
                    <p>تركيب 100 ميجاوات بنهج متعدد الطرق</p>
                </div>

                <div class="chart-container">
                    <canvas id="roiChart" width="800" height="400"></canvas>
                </div>

                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">18</div>
                        <div class="stat-label">شهر للاسترداد</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">410%</div>
                        <div class="stat-label">عائد 5 سنوات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6 مليون ريال</div>
                        <div class="stat-label">توفير سنوي</div>
                    </div>
                </div>
            </section>

            <!-- الجدولة التنبؤية -->
            <section>
                <h2>الجدولة التنبؤية المدعومة بالذكاء الاصطناعي</h2>
                <div class="gantt-container">
                    <h3>جدولة التنظيف الذكية - الـ 30 يوم القادمة</h3>
                    <div class="gantt-bar phase1">الأسبوع 1: التنظيف الكهروستاتيكي (الألواح أ1-أ50)</div>
                    <div class="gantt-bar phase2">الأسبوع 2: الروبوتات الزاحفة (الألواح ب1-ب30)</div>
                    <div class="gantt-bar phase3">الأسبوع 3: المعالجة بالأشعة فوق البنفسجية (الألواح ج1-ج40)</div>
                    <div class="gantt-bar phase4">الأسبوع 4: الطائرات الجافة (الألواح د1-د60)</div>
                </div>

                <h3>العوامل التنبؤية:</h3>
                <ul>
                    <li><strong>التنبؤ بالطقس:</strong> توقعات الغبار من CAMS</li>
                    <li><strong>مراقبة الكفاءة:</strong> بيانات الأداء في الوقت الفعلي</li>
                    <li><strong>الأنماط الموسمية:</strong> تراكم الغبار التاريخي</li>
                    <li><strong>تحسين التكلفة:</strong> اختيار الطريقة حسب العائد</li>
                    <li><strong>توفر الموارد:</strong> جدولة المعدات والطاقم</li>
                </ul>
            </section>

            <!-- المكدس التقني الكامل -->
            <section>
                <h2>المكدس التقني الكامل</h2>
                <div class="method-grid">
                    <div class="method-card">
                        <div class="method-icon">🤖</div>
                        <h3>كشف الذكاء الاصطناعي</h3>
                        <p>YOLOv9 + التصوير الحراري</p>
                        <small>دقة 99% في اكتشاف العيوب</small>
                    </div>

                    <div class="method-card">
                        <div class="method-icon">📱</div>
                        <h3>تطبيق الهاتف المحمول</h3>
                        <p>React Native متعدد المنصات</p>
                        <small>مراقبة وتحكم في الوقت الفعلي</small>
                    </div>

                    <div class="method-card">
                        <div class="method-icon">🌐</div>
                        <h3>لوحة التحكم الويب</h3>
                        <p>React + FastAPI خلفية</p>
                        <small>واجهة إدارة شاملة</small>
                    </div>

                    <div class="method-card">
                        <div class="method-icon">📡</div>
                        <h3>تكامل إنترنت الأشياء</h3>
                        <p>Arduino + Raspberry Pi</p>
                        <small>جمع بيانات الاستشعار في الوقت الفعلي</small>
                    </div>
                </div>
            </section>

            <!-- المزايا التنافسية -->
            <section>
                <h2>المزايا التنافسية</h2>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">الأول</div>
                        <div class="stat-label">تكامل الذكاء الاصطناعي</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50%</div>
                        <div class="stat-label">تقليل التكاليف</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">85%</div>
                        <div class="stat-label">توفير المياه</div>
                    </div>
                </div>

                <h3>الميزات الفريدة:</h3>
                <ul>
                    <li><strong>أول نظام شامل:</strong> يجمع بين الكشف والتنظيف والمراقبة</li>
                    <li><strong>تقنية متطورة:</strong> YOLOv9 للكشف بدقة 99%</li>
                    <li><strong>صديق للبيئة:</strong> تقليل استهلاك المياه بنسبة 85%</li>
                    <li><strong>اقتصادي:</strong> تقليل التكاليف بنسبة 50%</li>
                    <li><strong>ذكي:</strong> جدولة تنبؤية مدعومة بالذكاء الاصطناعي</li>
                    <li><strong>شامل:</strong> 9 طرق تنظيف مختلفة</li>
                </ul>
            </section>

            <!-- دعوة للعمل -->
            <section class="title-slide center">
                <h1>حوّل عمليات الطاقة الشمسية لديك</h1>
                <h2>انضم إلى مستقبل صيانة الألواح الشمسية</h2>

                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">50%</div>
                        <div class="stat-label">تقليل التكاليف</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">25%</div>
                        <div class="stat-label">زيادة الكفاءة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">85%</div>
                        <div class="stat-label">توفير المياه</div>
                    </div>
                </div>

                <div class="contact-info">
                    <h4>تواصل معنا اليوم للحصول على استشارة مجانية</h4>
                    <p><strong>📧 البريد الإلكتروني:</strong> <EMAIL></p>
                    <p><strong>📱 الهاتف:</strong> +966 50 123 4567</p>
                    <p><strong>🌐 الموقع الإلكتروني:</strong> www.solar-ai-monitoring.com</p>
                    <p><strong>📍 العنوان:</strong> الرياض، المملكة العربية السعودية</p>
                </div>

                <p style="font-size: 1.2em; margin-top: 30px; color: white;">
                    <strong>ابدأ رحلتك نحو مستقبل أكثر استدامة وكفاءة</strong>
                </p>
            </section>

            <!-- شكر وختام -->
            <section class="center">
                <h1 style="color: var(--primary-color); font-size: 3em;">شكراً لكم</h1>
                <h2 style="color: var(--secondary-color);">نحو مستقبل أكثر استدامة</h2>

                <div style="margin: 40px 0;">
                    <div style="font-size: 4em; margin-bottom: 20px;">🌞⚡🤖</div>
                    <p style="font-size: 1.5em; color: var(--dark-color); font-weight: 600;">
                        نظام مراقبة وتنظيف الألواح الشمسية بالذكاء الاصطناعي
                    </p>
                    <p style="font-size: 1.2em; color: var(--accent-color); margin-top: 20px;">
                        "الابتكار في خدمة الطاقة المتجددة"
                    </p>
                </div>

                <div class="stats-container" style="margin-top: 40px;">
                    <div class="stat-item">
                        <div class="stat-number" style="color: var(--secondary-color);">99%</div>
                        <div class="stat-label">دقة الكشف</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" style="color: var(--primary-color);">50%</div>
                        <div class="stat-label">توفير التكاليف</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" style="color: var(--accent-color);">85%</div>
                        <div class="stat-label">توفير المياه</div>
                    </div>
                </div>
            </section>

        </div>
    </div>

    <!-- Reveal.js JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.js"></script>
    <script>
        // Initialize Reveal.js
        Reveal.initialize({
            hash: true,
            controls: true,
            progress: true,
            center: false,
            transition: 'slide',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            rtl: true,

            // Plugins
            plugins: []
        });

        // ROI Chart
        const ctx = document.getElementById('roiChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['السنة 1', 'السنة 2', 'السنة 3', 'السنة 4', 'السنة 5'],
                    datasets: [{
                        label: 'التوفير التراكمي (مليون ريال)',
                        data: [2.5, 8.2, 15.6, 24.1, 30.8],
                        backgroundColor: 'rgba(30, 58, 138, 0.8)',
                        borderColor: 'rgba(30, 58, 138, 1)',
                        borderWidth: 2,
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                font: {
                                    family: 'Tajawal, Cairo, sans-serif',
                                    size: 14
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                font: {
                                    family: 'Tajawal, Cairo, sans-serif'
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    family: 'Tajawal, Cairo, sans-serif'
                                }
                            }
                        }
                    }
                }
            });
        }

        // Add animation classes when slides change
        Reveal.addEventListener('slidechanged', function(event) {
            // Remove existing animation classes
            const elements = event.currentSlide.querySelectorAll('.fade-in, .slide-in-right');
            elements.forEach(el => {
                el.style.animation = 'none';
                el.offsetHeight; // Trigger reflow
                el.style.animation = null;
            });
        });

        // Add hover effects to method cards
        document.addEventListener('DOMContentLoaded', function() {
            const methodCards = document.querySelectorAll('.method-card');
            methodCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
