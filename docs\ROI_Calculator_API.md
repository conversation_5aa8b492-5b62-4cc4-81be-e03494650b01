# 🧮 Enhanced ROI Calculator API Documentation

## Overview

The Enhanced ROI Calculator API provides comprehensive financial analysis for solar panel cleaning and monitoring systems. It supports detailed calculations for all 9 cleaning methods with advanced features including NPV, IRR, environmental impact analysis, and risk assessment.

## 🌟 Key Features

- **Comprehensive Analysis**: Monthly and yearly financial projections
- **9 Cleaning Methods**: Support for all advanced cleaning technologies
- **Environmental Impact**: Water savings and carbon footprint analysis
- **Risk Assessment**: Best/worst case scenarios and sensitivity analysis
- **Method Comparison**: Side-by-side analysis of all cleaning methods
- **NPV & IRR**: Advanced financial metrics calculation
- **Payback Analysis**: Detailed payback period calculation

---

## 📊 API Endpoints

### 1. Detailed ROI Calculation

**Endpoint**: `POST /api/roi/calculate-detailed`

**Description**: Calculate comprehensive ROI analysis with detailed monthly/yearly breakdown

**Request Body**:
```json
{
  "system_config": {
    "system_size_mw": 100.0,
    "location": "Riyadh, Saudi Arabia",
    "installation_year": 2024,
    "panel_type": "monocrystalline",
    "inverter_efficiency": 0.96,
    "system_degradation_rate": 0.005
  },
  "environmental_factors": {
    "average_irradiance": 6.2,
    "dust_accumulation_rate": 2.0,
    "rainfall_days_per_year": 15,
    "wind_speed_average": 4.5,
    "humidity_average": 35.0
  },
  "financial_params": {
    "electricity_price_per_kwh": 0.08,
    "electricity_price_escalation": 0.03,
    "discount_rate": 0.08,
    "tax_rate": 0.25,
    "depreciation_years": 10
  },
  "cleaning_costs": {
    "method": "drone_waterless",
    "cost_per_mw": 120.0,
    "frequency_per_year": 12,
    "water_cost_per_liter": 0.001,
    "labor_cost_per_hour": 25.0,
    "equipment_depreciation": 0.1
  },
  "analysis_years": 25
}
```

**Response**:
```json
{
  "request_id": "roi_20241220_143022",
  "calculation_date": "2024-12-20T14:30:22",
  "total_investment": 12500000.0,
  "total_savings": 51200000.0,
  "net_present_value": 28750000.0,
  "internal_rate_of_return": 35.2,
  "payback_period_months": 18.5,
  "roi_5_years": 410.0,
  "roi_10_years": 820.0,
  "roi_25_years": 1650.0,
  "monthly_data": [...],
  "yearly_data": [...],
  "water_saved_liters": 2500000.0,
  "carbon_reduced_kg": 7500000.0,
  "energy_efficiency_gain": 90.0,
  "best_case_roi": 533.0,
  "worst_case_roi": 287.0,
  "sensitivity_analysis": {...}
}
```

### 2. Method Comparison

**Endpoint**: `POST /api/roi/compare-methods`

**Description**: Compare ROI for all 9 cleaning methods

**Request Body**: Same as detailed calculation

**Response**:
```json
[
  {
    "method": "crawler_robots",
    "total_cost_5_years": 4800000.0,
    "total_savings_5_years": 25600000.0,
    "net_benefit_5_years": 20800000.0,
    "roi_5_years": 433.3,
    "payback_period_months": 17.2,
    "efficiency_improvement": 95.0,
    "water_usage_liters": 600000.0,
    "carbon_footprint_kg": 8200000.0
  },
  {
    "method": "drone_waterless",
    "total_cost_5_years": 7200000.0,
    "total_savings_5_years": 23040000.0,
    "net_benefit_5_years": 15840000.0,
    "roi_5_years": 220.0,
    "payback_period_months": 22.7,
    "efficiency_improvement": 90.0,
    "water_usage_liters": 0.0,
    "carbon_footprint_kg": 7650000.0
  }
]
```

### 3. Quick ROI Estimate

**Endpoint**: `POST /api/roi/quick-estimate`

**Description**: Quick ROI estimate for basic analysis

**Parameters**:
- `system_size_mw` (float): System size in megawatts
- `location` (string): Installation location
- `electricity_price` (float): Electricity price per kWh
- `method` (string): Cleaning method (optional, default: "drone_waterless")

**Response**:
```json
{
  "system_size_mw": 100.0,
  "location": "Riyadh",
  "method": "drone_waterless",
  "roi_5_years": 410.0,
  "payback_period_months": 18.5,
  "total_savings": 51200000.0,
  "total_investment": 12500000.0,
  "net_present_value": 28750000.0,
  "water_saved_liters": 2500000.0,
  "carbon_reduced_kg": 7500000.0,
  "calculation_date": "2024-12-20T14:30:22"
}
```

### 4. Cleaning Method Specifications

**Endpoint**: `GET /api/roi/cleaning-methods`

**Description**: Get specifications for all cleaning methods

**Response**:
```json
{
  "methods": [
    "drone_water",
    "drone_waterless",
    "crawler_robots",
    "nano_coatings",
    "air_blowers",
    "ultrasonic_vibrations",
    "electrostatic_cleaning",
    "uv_surface_cleaning",
    "predictive_maintenance"
  ],
  "specifications": {
    "drone_waterless": {
      "efficiency": 0.90,
      "water_usage": 0.0,
      "duration_hours": 0.5,
      "equipment_cost": 75000,
      "maintenance_factor": 1.0
    }
  },
  "total_methods": 9
}
```

---

## 🧹 Cleaning Methods Supported

| Method | Efficiency | Water Usage | Duration | Equipment Cost | Best For |
|--------|------------|-------------|----------|----------------|----------|
| **Crawler Robots** | 95% | 0.1 L/m² | 60 min | $100K/MW | Maximum efficiency |
| **Drone Waterless** | 90% | 0.0 L/m² | 30 min | $75K/MW | Water conservation |
| **Drone Water** | 85% | 0.7 L/m² | 45 min | $50K/MW | Cost-effective |
| **Electrostatic** | 85% | 0.0 L/m² | 10 min | $80K/MW | Speed & efficiency |
| **Ultrasonic** | 80% | 0.0 L/m² | 15 min | $60K/MW | Gentle cleaning |
| **Nano-Coatings** | 75% | 0.0 L/m² | 0 min | $200K/MW | Long-term solution |
| **Air Blowers** | 70% | 0.0 L/m² | 20 min | $30K/MW | Budget option |
| **UV Surface** | 65% | 0.0 L/m² | 25 min | $40K/MW | Organic contamination |
| **Predictive AI** | 92% | 0.05 L/m² | Variable | $120K/MW | Optimal scheduling |

---

## 📈 Financial Metrics Explained

### **ROI (Return on Investment)**
```
ROI = (Total Savings - Total Investment) / Total Investment × 100
```

### **NPV (Net Present Value)**
```
NPV = Σ(Cash Flow / (1 + Discount Rate)^Year)
```

### **IRR (Internal Rate of Return)**
The discount rate that makes NPV = 0

### **Payback Period**
Time required for cumulative savings to equal initial investment

---

## 🌍 Environmental Impact Calculations

### **Water Savings**
```
Water Saved = (Traditional Usage - Method Usage) × Panel Area × Cleanings × Years
```

### **Carbon Reduction**
```
Carbon Reduced = Energy Recovered × Grid Carbon Intensity
```

### **Energy Efficiency Gain**
```
Efficiency Gain = Method Efficiency × Dust Recovery Rate
```

---

## 🔍 Risk Analysis Features

### **Sensitivity Analysis**
Tests impact of ±20% changes in:
- Electricity prices
- Cleaning costs
- System size
- Dust accumulation rates

### **Scenario Analysis**
- **Best Case**: 20% better performance, 10% lower costs
- **Worst Case**: 20% worse performance, 15% higher costs

---

## 💡 Usage Examples

### **Python Example**
```python
import requests

# Authentication
auth_response = requests.post("http://localhost:8000/api/auth/login", 
                            json={"username": "admin", "password": "secret"})
token = auth_response.json()["access_token"]
headers = {"Authorization": f"Bearer {token}"}

# Quick estimate
response = requests.post("http://localhost:8000/api/roi/quick-estimate",
                        params={
                            "system_size_mw": 100,
                            "location": "Riyadh",
                            "electricity_price": 0.08,
                            "method": "drone_waterless"
                        },
                        headers=headers)

result = response.json()
print(f"ROI: {result['roi_5_years']}%")
print(f"Payback: {result['payback_period_months']} months")
```

### **JavaScript Example**
```javascript
// Authentication
const authResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'admin', password: 'secret' })
});
const { access_token } = await authResponse.json();

// Method comparison
const comparisonResponse = await fetch('/api/roi/compare-methods', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${access_token}`
  },
  body: JSON.stringify(roiRequest)
});

const comparisons = await comparisonResponse.json();
console.log('Best method:', comparisons[0].method);
```

---

## 🚀 Getting Started

1. **Start the API server**:
   ```bash
   cd backend
   uvicorn app.main:app --reload
   ```

2. **Test the API**:
   ```bash
   python scripts/test_roi_calculator.py
   ```

3. **Access documentation**:
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

---

## 📞 Support

For technical support or questions about the ROI Calculator API:

- **Email**: <EMAIL>
- **Documentation**: [API Reference](http://localhost:8000/docs)
- **GitHub Issues**: [Report Issues](https://github.com/solar-ai-monitoring/issues)

---

## 🔄 Version History

- **v2.0.0**: Enhanced ROI Calculator with 9 cleaning methods
- **v1.5.0**: Added environmental impact analysis
- **v1.0.0**: Basic ROI calculation functionality

---

**🌞 Built for the future of sustainable solar energy management 🤖**
