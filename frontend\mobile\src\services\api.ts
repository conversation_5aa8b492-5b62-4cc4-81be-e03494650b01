import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  PanelStatus, 
  SystemStats, 
  DustData, 
  Notification, 
  WeatherData,
  ApiResponse,
  PaginatedResponse,
  ROICalculation,
  User
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL = 'http://192.168.1.100:8000'; // Replace with your actual API URL

  constructor() {
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired, redirect to login
          await AsyncStorage.removeItem('auth_token');
          // Navigate to login screen
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication
  async login(username: string, password: string): Promise<ApiResponse<{ token: string; user: User }>> {
    const response = await this.api.post('/api/auth/login', { username, password });
    return response.data;
  }

  async logout(): Promise<void> {
    await this.api.post('/api/auth/logout');
    await AsyncStorage.removeItem('auth_token');
  }

  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    const response = await this.api.post('/api/auth/refresh');
    return response.data;
  }

  // Panel Management
  async getPanelStatus(): Promise<PanelStatus[]> {
    const response = await this.api.get('/api/panel-status');
    return response.data;
  }

  async getPanelById(panelId: string): Promise<PanelStatus> {
    const response = await this.api.get(`/api/panel-status/${panelId}`);
    return response.data;
  }

  async updatePanelStatus(panelId: string, updates: Partial<PanelStatus>): Promise<ApiResponse<string>> {
    const response = await this.api.post(`/api/panel-status/${panelId}/update`, updates);
    return response.data;
  }

  async getPanelHistory(panelId: string, days: number = 7): Promise<DustData[]> {
    const response = await this.api.get(`/api/panel/${panelId}/history?days=${days}`);
    return response.data;
  }

  // System Statistics
  async getSystemStats(): Promise<SystemStats> {
    const response = await this.api.get('/api/stats');
    return response.data;
  }

  // Dust Data
  async getCurrentDustData(): Promise<DustData> {
    const response = await this.api.get('/api/dust-data/current');
    return response.data;
  }

  async getDustDataHistory(hours: number = 24): Promise<DustData[]> {
    const response = await this.api.get(`/api/dust-data/history?hours=${hours}`);
    return response.data;
  }

  async getDustForecast(): Promise<number[]> {
    const response = await this.api.get('/api/dust-data/forecast');
    return response.data;
  }

  // CAMS Data
  async getCAMSData(): Promise<any> {
    const response = await this.api.get('/api/cams/current');
    return response.data;
  }

  async getCAMSForecast(): Promise<any> {
    const response = await this.api.get('/api/cams/forecast');
    return response.data;
  }

  // Weather Data
  async getCurrentWeather(): Promise<WeatherData> {
    const response = await this.api.get('/api/weather/current');
    return response.data;
  }

  async getWeatherForecast(days: number = 7): Promise<WeatherData[]> {
    const response = await this.api.get(`/api/weather/forecast?days=${days}`);
    return response.data;
  }

  // Cleaning Operations
  async scheduleCleaning(panelIds: string[]): Promise<ApiResponse<string>> {
    const response = await this.api.post('/api/cleaning/schedule', { panel_ids: panelIds });
    return response.data;
  }

  async getCleaningHistory(panelId?: string): Promise<any[]> {
    const url = panelId ? `/api/cleaning/history?panel_id=${panelId}` : '/api/cleaning/history';
    const response = await this.api.get(url);
    return response.data;
  }

  async cancelCleaning(cleaningId: string): Promise<ApiResponse<string>> {
    const response = await this.api.delete(`/api/cleaning/${cleaningId}`);
    return response.data;
  }

  // Notifications
  async getNotifications(page: number = 1, limit: number = 20): Promise<PaginatedResponse<Notification>> {
    const response = await this.api.get(`/api/notifications?page=${page}&limit=${limit}`);
    return response.data;
  }

  async markNotificationRead(notificationId: string): Promise<ApiResponse<string>> {
    const response = await this.api.put(`/api/notifications/${notificationId}/read`);
    return response.data;
  }

  async markAllNotificationsRead(): Promise<ApiResponse<string>> {
    const response = await this.api.put('/api/notifications/mark-all-read');
    return response.data;
  }

  async getNotificationSettings(): Promise<any> {
    const response = await this.api.get('/api/notifications/settings');
    return response.data;
  }

  async updateNotificationSettings(settings: any): Promise<ApiResponse<string>> {
    const response = await this.api.put('/api/notifications/settings', settings);
    return response.data;
  }

  // ROI Calculator
  async calculateROI(params: {
    system_size_mw: number;
    current_efficiency_loss: number;
    electricity_price_per_kwh: number;
    cleaning_frequency_per_year: number;
    maintenance_cost_per_cleaning: number;
  }): Promise<ROICalculation> {
    const response = await this.api.post('/api/roi/calculate', params);
    return response.data;
  }

  async saveROICalculation(calculation: ROICalculation): Promise<ApiResponse<string>> {
    const response = await this.api.post('/api/roi/save', calculation);
    return response.data;
  }

  async getROIHistory(): Promise<ROICalculation[]> {
    const response = await this.api.get('/api/roi/history');
    return response.data;
  }

  // Reports
  async generateReport(type: 'daily' | 'weekly' | 'monthly', startDate: string, endDate: string): Promise<any> {
    const response = await this.api.post('/api/reports/generate', {
      type,
      start_date: startDate,
      end_date: endDate,
    });
    return response.data;
  }

  async downloadReport(reportId: string): Promise<Blob> {
    const response = await this.api.get(`/api/reports/${reportId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // User Management
  async getUserProfile(): Promise<User> {
    const response = await this.api.get('/api/user/profile');
    return response.data;
  }

  async updateUserProfile(updates: Partial<User>): Promise<ApiResponse<string>> {
    const response = await this.api.put('/api/user/profile', updates);
    return response.data;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<string>> {
    const response = await this.api.put('/api/user/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
    return response.data;
  }

  // Device Registration for Push Notifications
  async registerDevice(deviceToken: string, platform: 'ios' | 'android'): Promise<ApiResponse<string>> {
    const response = await this.api.post('/api/devices/register', {
      device_token: deviceToken,
      platform,
    });
    return response.data;
  }

  async unregisterDevice(deviceToken: string): Promise<ApiResponse<string>> {
    const response = await this.api.delete('/api/devices/unregister', {
      data: { device_token: deviceToken },
    });
    return response.data;
  }

  // Health Check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await this.api.get('/api/health');
    return response.data;
  }

  // WebSocket URL for real-time updates
  getWebSocketURL(): string {
    return this.baseURL.replace('http', 'ws') + '/ws';
  }
}

export default new ApiService();
