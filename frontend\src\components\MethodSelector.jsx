import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { 
  Zap, 
  Droplets, 
  Clock, 
  DollarSign, 
  Bot, 
  Wind, 
  Waves, 
  Sparkles,
  Sun,
  Cpu
} from 'lucide-react';

const MethodSelector = ({ selectedPanels = [], onMethodSelect }) => {
  const [selectedMethod, setSelectedMethod] = useState('');
  const [cleaningMethods, setCleaningMethods] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchCleaningMethods();
  }, []);

  const fetchCleaningMethods = async () => {
    try {
      const response = await fetch('/api/cleaning/methods');
      const methods = await response.json();
      setCleaningMethods(methods);
    } catch (error) {
      console.error('Failed to fetch cleaning methods:', error);
    }
  };

  const getMethodIcon = (method) => {
    const iconMap = {
      drone_water: <Droplets className="h-5 w-5" />,
      drone_waterless: <Zap className="h-5 w-5" />,
      crawler_robots: <Bot className="h-5 w-5" />,
      nano_coatings: <Sparkles className="h-5 w-5" />,
      air_blowers: <Wind className="h-5 w-5" />,
      ultrasonic_vibrations: <Waves className="h-5 w-5" />,
      electrostatic_cleaning: <Zap className="h-5 w-5" />,
      uv_surface_cleaning: <Sun className="h-5 w-5" />,
      predictive_maintenance: <Cpu className="h-5 w-5" />
    };
    return iconMap[method] || <Zap className="h-5 w-5" />;
  };

  const getEfficiencyColor = (efficiency) => {
    if (efficiency >= 90) return 'bg-green-500';
    if (efficiency >= 80) return 'bg-blue-500';
    if (efficiency >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const handleMethodSelection = async () => {
    if (!selectedMethod || selectedPanels.length === 0) return;

    setLoading(true);
    try {
      const response = await fetch('/api/select-method', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          panel_ids: selectedPanels,
          method: selectedMethod,
          priority: 'medium'
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        onMethodSelect?.(result);
        // Show success notification
        alert(`Cleaning method selected successfully! Estimated cost: $${result.estimated_cost}`);
      } else {
        throw new Error(result.detail || 'Failed to select method');
      }
    } catch (error) {
      console.error('Failed to select cleaning method:', error);
      alert('Failed to select cleaning method. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Cpu className="h-5 w-5" />
          Cleaning Method Selector
        </CardTitle>
        <CardDescription>
          Choose the optimal cleaning method for {selectedPanels.length} selected panel(s)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Method Selection */}
        <div className="space-y-4">
          <label className="text-sm font-medium">Select Cleaning Method:</label>
          <Select value={selectedMethod} onValueChange={setSelectedMethod}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a cleaning method" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(cleaningMethods).map(([key, method]) => (
                <SelectItem key={key} value={key}>
                  <div className="flex items-center gap-2">
                    {getMethodIcon(key)}
                    {method.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Method Details */}
        {selectedMethod && cleaningMethods[selectedMethod] && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {getMethodIcon(selectedMethod)}
              <h3 className="font-semibold">{cleaningMethods[selectedMethod].name}</h3>
            </div>
            
            <p className="text-sm text-gray-600">
              {cleaningMethods[selectedMethod].description}
            </p>

            {/* Method Specifications */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Zap className="h-4 w-4 text-blue-500" />
                  <span className="text-xs font-medium">Efficiency</span>
                </div>
                <Badge className={`${getEfficiencyColor(cleaningMethods[selectedMethod].efficiency)} text-white`}>
                  {cleaningMethods[selectedMethod].efficiency}%
                </Badge>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Droplets className="h-4 w-4 text-blue-500" />
                  <span className="text-xs font-medium">Water Usage</span>
                </div>
                <Badge variant="outline">
                  {cleaningMethods[selectedMethod].water_usage} L/m²
                </Badge>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <span className="text-xs font-medium">Duration</span>
                </div>
                <Badge variant="outline">
                  {cleaningMethods[selectedMethod].duration_minutes} min
                </Badge>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <DollarSign className="h-4 w-4 text-blue-500" />
                  <span className="text-xs font-medium">Cost/MW</span>
                </div>
                <Badge variant="outline">
                  ${cleaningMethods[selectedMethod].cost_per_mw}
                </Badge>
              </div>
            </div>

            {/* Weather Dependency */}
            {cleaningMethods[selectedMethod].weather_dependent && (
              <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded border border-yellow-200">
                <Sun className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">
                  Weather dependent - scheduling will consider weather conditions
                </span>
              </div>
            )}

            {/* Cost Estimate */}
            {selectedPanels.length > 0 && (
              <div className="p-3 bg-blue-50 rounded border border-blue-200">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-blue-900">Estimated Total Cost:</span>
                  <span className="text-lg font-bold text-blue-900">
                    ${(selectedPanels.length * cleaningMethods[selectedMethod].cost_per_mw).toFixed(2)}
                  </span>
                </div>
                <div className="text-sm text-blue-700 mt-1">
                  For {selectedPanels.length} panel(s) × ${cleaningMethods[selectedMethod].cost_per_mw}/panel
                </div>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button 
            onClick={handleMethodSelection}
            disabled={!selectedMethod || selectedPanels.length === 0 || loading}
            className="flex-1"
          >
            {loading ? 'Scheduling...' : 'Schedule Cleaning'}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => setSelectedMethod('')}
            disabled={loading}
          >
            Clear
          </Button>
        </div>

        {/* Selection Info */}
        <div className="text-sm text-gray-500 text-center">
          {selectedPanels.length === 0 
            ? 'Select panels from the dashboard to enable method selection'
            : `${selectedPanels.length} panel(s) selected for cleaning`
          }
        </div>
      </CardContent>
    </Card>
  );
};

export default MethodSelector;
