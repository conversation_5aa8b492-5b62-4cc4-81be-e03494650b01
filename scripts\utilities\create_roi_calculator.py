import openpyxl

def create_roi_calculator(filename="roi_calculator.xlsx"):
    """
    Creates an Excel spreadsheet for calculating ROI of solar panel cleaning.
    """
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.title = "ROI Calculator"

    # Headers
    sheet['A1'] = "Parameter"
    sheet['B1'] = "Value"
    sheet['C1'] = "Unit"

    # Input Parameters
    sheet['A3'] = "Average Daily Energy Production"
    sheet['B3'] = 1000
    sheet['C3'] = "kWh/day"

    sheet['A4'] = "Average Dust Accumulation Loss"
    sheet['B4'] = 0.15  # 15%
    sheet['C4'] = "%"

    sheet['A5'] = "Cost of Electricity"
    sheet['B5'] = 0.10
    sheet['C5'] = "$/kWh"

    sheet['A6'] = "Cleaning Frequency"
    sheet['B6'] = 12  # once a month
    sheet['C6'] = "times/year"

    sheet['A7'] = "Cost per Cleaning"
    sheet['B7'] = 500
    sheet['C7'] = "$"

    # Calculated Values
    sheet['A9'] = "Annual Energy Loss due to Dust"
    sheet['B9'] = "=B3*365*B4"
    sheet['C9'] = "kWh/year"

    sheet['A10'] = "Annual Revenue Loss due to Dust"
    sheet['B10'] = "=B9*B5"
    sheet['C10'] = "$"

    sheet['A11'] = "Annual Cleaning Cost"
    sheet['B11'] = "=B6*B7"
    sheet['C11'] = "$"

    sheet['A12'] = "Potential Annual Savings (before cleaning cost)"
    sheet['B12'] = "=B10"
    sheet['C12'] = "$"

    sheet['A13'] = "Net Annual Savings (after cleaning cost)"
    sheet['B13'] = "=B10-B11"
    sheet['C13'] = "$"

    # Formatting
    sheet.column_dimensions['A'].width = 40
    sheet.column_dimensions['B'].width = 15
    sheet.column_dimensions['C'].width = 15

    workbook.save(filename)
    print(f"ROI calculator saved to {filename}")

if __name__ == "__main__":
    create_roi_calculator("/home/<USER>/solar_project/roi_calculator.xlsx")


