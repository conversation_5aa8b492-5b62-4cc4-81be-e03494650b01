#!/bin/bash

# Daily Solar Panel Monitoring Cron Script
# This script runs daily monitoring tasks for the solar panel system
# Add to crontab with: 0 6 * * * /path/to/daily_monitoring.sh

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Set up logging
LOG_FILE="logs/daily_monitoring_$(date +%Y%m%d).log"
mkdir -p logs

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_message "Starting daily solar panel monitoring"

# Activate virtual environment if it exists
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
    log_message "Activated virtual environment"
fi

# Download latest CAMS dust data
log_message "Downloading CAMS dust data..."
if python3 scripts/download_cams_data.py; then
    log_message "CAMS data download completed successfully"
else
    log_message "ERROR: CAMS data download failed"
fi

# Process dust forecast
log_message "Processing dust forecast..."
if python3 cams_to_json.py; then
    log_message "Dust forecast processing completed successfully"
else
    log_message "ERROR: Dust forecast processing failed"
fi

# Check panel production and send notifications if needed
log_message "Checking panel production..."
if python3 notify.py --check-production; then
    log_message "Production check completed successfully"
else
    log_message "ERROR: Production check failed"
fi

# Update battery-based cleaning schedule
log_message "Updating cleaning schedule..."
if python3 battery_scheduler.py; then
    log_message "Cleaning schedule update completed successfully"
else
    log_message "ERROR: Cleaning schedule update failed"
fi

# Archive old log files (keep last 30 days)
find logs/ -name "daily_monitoring_*.log" -mtime +30 -delete

log_message "Daily monitoring completed"

# Send summary email (optional)
if command -v mail &> /dev/null; then
    tail -20 "$LOG_FILE" | mail -s "Solar Panel Daily Monitoring Summary" <EMAIL>
fi

