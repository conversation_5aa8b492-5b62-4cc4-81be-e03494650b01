#!/usr/bin/env python3
"""
Quick tests for the FastAPI application.

This module contains basic tests for the FastAPI endpoints to ensure
they are functioning correctly.

Author: Solar AI Cleaning & Monitoring Team
License: MIT
"""

import os
import sys
import unittest
from fastapi.testclient import TestClient

# Add parent directory to path to import fastapi_app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from fastapi_app import app

class TestFastAPIApp(unittest.TestCase):
    """Test cases for the FastAPI application."""

    def setUp(self):
        """Set up test client and sample data."""
        self.client = TestClient(app)
        self.sample_panel_id = "A23"
        self.sample_update_data = {
            "dust_level": 15.0,
            "efficiency": 85.0,
            "status": "dusty"
        }

    def test_get_panel_status(self):
        """Test GET /api/panel-status endpoint."""
        response = self.client.get("/api/panel-status")
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        # Check if we have at least one panel in the response
        self.assertGreater(len(response.json()), 0)

    def test_get_specific_panel(self):
        """Test GET /api/panel-status/{panel_id} endpoint."""
        response = self.client.get(f"/api/panel-status/{self.sample_panel_id}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["panel_id"], self.sample_panel_id)

    def test_get_nonexistent_panel(self):
        """Test GET with non-existent panel ID."""
        response = self.client.get("/api/panel-status/NONEXISTENT")
        self.assertEqual(response.status_code, 404)

    def test_update_panel_status(self):
        """Test POST /api/panel-status/{panel_id}/update endpoint."""
        response = self.client.post(
            f"/api/panel-status/{self.sample_panel_id}/update",
            json=self.sample_update_data
        )
        self.assertEqual(response.status_code, 200)
        updated_panel = response.json()
        self.assertEqual(updated_panel["panel_id"], self.sample_panel_id)
        self.assertEqual(updated_panel["dust_level"], self.sample_update_data["dust_level"])
        self.assertEqual(updated_panel["efficiency"], self.sample_update_data["efficiency"])
        self.assertEqual(updated_panel["status"], self.sample_update_data["status"])

    def test_update_invalid_data(self):
        """Test POST with invalid data."""
        invalid_data = {"dust_level": "not_a_number"}
        response = self.client.post(
            f"/api/panel-status/{self.sample_panel_id}/update",
            json=invalid_data
        )
        self.assertEqual(response.status_code, 422)  # Validation error

    def test_get_system_stats(self):
        """Test GET /api/stats endpoint."""
        response = self.client.get("/api/stats")
        self.assertEqual(response.status_code, 200)
        stats = response.json()
        self.assertIn("total_panels", stats)
        self.assertIn("active_panels", stats)
        self.assertIn("average_efficiency", stats)
        self.assertIn("cleaning_scheduled", stats)

if __name__ == "__main__":
    unittest.main()

