import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { 
  Thermometer, 
  Droplets, 
  Zap, 
  Activity, 
  Wifi, 
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';

const LiveSensorWidgets = ({ panelId = null, refreshInterval = 5000 }) => {
  const [sensorData, setSensorData] = useState({});
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('connected');

  useEffect(() => {
    fetchSensorData();
    const interval = setInterval(fetchSensorData, refreshInterval);
    return () => clearInterval(interval);
  }, [panelId, refreshInterval]);

  const fetchSensorData = async () => {
    try {
      const url = panelId ? `/api/sensors/live?panel_id=${panelId}` : '/api/sensors/live';
      const response = await fetch(url);
      
      if (response.ok) {
        const data = await response.json();
        setSensorData(data);
        setLastUpdate(new Date());
        setConnectionStatus('connected');
      } else {
        setConnectionStatus('error');
      }
    } catch (error) {
      console.error('Failed to fetch sensor data:', error);
      setConnectionStatus('disconnected');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'text-green-500';
      case 'disconnected': return 'text-red-500';
      case 'error': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return <Wifi className="h-4 w-4" />;
      case 'disconnected': return <AlertTriangle className="h-4 w-4" />;
      case 'error': return <AlertTriangle className="h-4 w-4" />;
      default: return <Wifi className="h-4 w-4" />;
    }
  };

  const getTrendIcon = (current, previous) => {
    if (!previous) return <Minus className="h-3 w-3 text-gray-400" />;
    if (current > previous) return <TrendingUp className="h-3 w-3 text-green-500" />;
    if (current < previous) return <TrendingDown className="h-3 w-3 text-red-500" />;
    return <Minus className="h-3 w-3 text-gray-400" />;
  };

  const getTemperatureStatus = (temp) => {
    if (temp > 45) return { status: 'critical', color: 'bg-red-500' };
    if (temp > 35) return { status: 'warning', color: 'bg-yellow-500' };
    return { status: 'normal', color: 'bg-green-500' };
  };

  const getVoltageStatus = (voltage) => {
    if (voltage < 10) return { status: 'low', color: 'bg-red-500' };
    if (voltage < 11) return { status: 'warning', color: 'bg-yellow-500' };
    return { status: 'normal', color: 'bg-green-500' };
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // Get latest sensor reading for display
  const getLatestReading = (panelData) => {
    if (!panelData || panelData.length === 0) return null;
    return panelData[panelData.length - 1];
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Connection Status Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Live Sensor Data</CardTitle>
            <div className={`flex items-center gap-2 ${getStatusColor(connectionStatus)}`}>
              {getStatusIcon(connectionStatus)}
              <span className="text-sm font-medium capitalize">{connectionStatus}</span>
            </div>
          </div>
          {lastUpdate && (
            <CardDescription>
              Last updated: {lastUpdate.toLocaleTimeString()}
            </CardDescription>
          )}
        </CardHeader>
      </Card>

      {/* Sensor Widgets Grid */}
      {panelId ? (
        // Single panel view
        <SinglePanelSensors 
          panelId={panelId} 
          sensorData={getLatestReading(sensorData)} 
        />
      ) : (
        // Multi-panel overview
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(sensorData).map(([pid, readings]) => {
            const latestReading = getLatestReading(readings);
            return (
              <PanelSensorCard 
                key={pid} 
                panelId={pid} 
                sensorData={latestReading} 
              />
            );
          })}
        </div>
      )}

      {Object.keys(sensorData).length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No sensor data available</p>
            <p className="text-sm text-gray-400 mt-2">
              Check sensor connections and try again
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

const SinglePanelSensors = ({ panelId, sensorData }) => {
  if (!sensorData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">No data available for panel {panelId}</p>
        </CardContent>
      </Card>
    );
  }

  const tempStatus = getTemperatureStatus(sensorData.temperature);
  const voltageStatus = getVoltageStatus(sensorData.voltage);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Temperature */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Thermometer className="h-4 w-4 text-red-500" />
              <span className="text-sm font-medium">Temperature</span>
            </div>
            <Badge className={tempStatus.color}>
              {tempStatus.status}
            </Badge>
          </div>
          <div className="text-2xl font-bold">{sensorData.temperature.toFixed(1)}°C</div>
          <div className="text-xs text-gray-500 mt-1">
            {formatTimestamp(sensorData.timestamp)}
          </div>
        </CardContent>
      </Card>

      {/* Humidity */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <Droplets className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium">Humidity</span>
          </div>
          <div className="text-2xl font-bold">{sensorData.humidity.toFixed(1)}%</div>
          <Progress value={sensorData.humidity} className="mt-2" />
          <div className="text-xs text-gray-500 mt-1">
            {formatTimestamp(sensorData.timestamp)}
          </div>
        </CardContent>
      </Card>

      {/* Voltage */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium">Voltage</span>
            </div>
            <Badge className={voltageStatus.color}>
              {voltageStatus.status}
            </Badge>
          </div>
          <div className="text-2xl font-bold">{sensorData.voltage.toFixed(2)}V</div>
          <div className="text-xs text-gray-500 mt-1">
            {formatTimestamp(sensorData.timestamp)}
          </div>
        </CardContent>
      </Card>

      {/* Power */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <Activity className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium">Power</span>
          </div>
          <div className="text-2xl font-bold">{sensorData.power.toFixed(1)}W</div>
          <div className="text-xs text-gray-500 mt-1">
            Current: {sensorData.current.toFixed(2)}A
          </div>
          <div className="text-xs text-gray-500">
            {formatTimestamp(sensorData.timestamp)}
          </div>
        </CardContent>
      </Card>

      {/* Additional Sensors */}
      {sensorData.vibration !== undefined && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Vibration</span>
            </div>
            <div className="text-2xl font-bold">{sensorData.vibration.toFixed(2)}</div>
            <div className="text-xs text-gray-500 mt-1">
              {formatTimestamp(sensorData.timestamp)}
            </div>
          </CardContent>
        </Card>
      )}

      {sensorData.dust_density !== undefined && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Droplets className="h-4 w-4 text-orange-500" />
              <span className="text-sm font-medium">Dust Density</span>
            </div>
            <div className="text-2xl font-bold">{sensorData.dust_density.toFixed(1)}</div>
            <div className="text-xs text-gray-500 mt-1">
              μg/m³
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

const PanelSensorCard = ({ panelId, sensorData }) => {
  if (!sensorData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Panel {panelId}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-gray-500">No data</p>
        </CardContent>
      </Card>
    );
  }

  const tempStatus = getTemperatureStatus(sensorData.temperature);

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">Panel {panelId}</CardTitle>
        <CardDescription className="text-xs">
          {formatTimestamp(sensorData.timestamp)}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-xs">Temp:</span>
          <Badge className={tempStatus.color} variant="outline">
            {sensorData.temperature.toFixed(1)}°C
          </Badge>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-xs">Voltage:</span>
          <span className="text-xs font-medium">{sensorData.voltage.toFixed(2)}V</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-xs">Power:</span>
          <span className="text-xs font-medium">{sensorData.power.toFixed(1)}W</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default LiveSensorWidgets;
