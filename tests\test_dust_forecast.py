#!/usr/bin/env python3
"""
Quick tests for the dust forecast module.

This module contains basic tests for the dust forecast functionality
to ensure it processes data correctly.

Author: Solar AI Cleaning & Monitoring Team
License: MIT
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock

# Add parent directory to path to import dust_forecast
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import dust_forecast

class TestDustForecast(unittest.TestCase):
    """Test cases for the dust forecast module."""

    def setUp(self):
        """Set up test data."""
        self.test_coordinates = {"lat": 24.7136, "lon": 46.6753}
        self.test_api_key = "test_api_key"
        
        # Sample forecast data structure
        self.sample_forecast = {
            "location": {
                "latitude": 24.7136,
                "longitude": 46.6753,
                "name": "Riyadh, Saudi Arabia"
            },
            "forecast": [
                {
                    "timestamp": "2025-06-19T00:00:00Z",
                    "dust_concentration": 45.2,
                    "visibility": 8.5,
                    "wind_speed": 12.3
                },
                {
                    "timestamp": "2025-06-19T03:00:00Z",
                    "dust_concentration": 52.8,
                    "visibility": 7.2,
                    "wind_speed": 14.5
                }
            ]
        }

    @patch('dust_forecast.requests.get')
    def test_get_dust_forecast(self, mock_get):
        """Test retrieving dust forecast data."""
        # Configure the mock to return a successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = self.sample_forecast
        mock_get.return_value = mock_response
        
        # Call the function with test parameters
        result = dust_forecast.get_dust_forecast(
            self.test_coordinates["lat"],
            self.test_coordinates["lon"],
            self.test_api_key
        )
        
        # Verify the result
        self.assertEqual(result, self.sample_forecast)
        self.assertEqual(len(result["forecast"]), 2)
        self.assertEqual(result["location"]["name"], "Riyadh, Saudi Arabia")
        
        # Verify the API was called with correct parameters
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        self.assertIn("lat=24.7136", kwargs["params"])
        self.assertIn("lon=46.6753", kwargs["params"])
        self.assertIn("key=test_api_key", kwargs["params"])

    @patch('dust_forecast.requests.get')
    def test_api_error_handling(self, mock_get):
        """Test error handling when API returns an error."""
        # Configure the mock to return an error response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        # Call the function and verify it handles the error
        result = dust_forecast.get_dust_forecast(
            self.test_coordinates["lat"],
            self.test_coordinates["lon"],
            self.test_api_key
        )
        
        # Should return None or an error indicator
        self.assertIsNone(result)

    def test_process_forecast_data(self):
        """Test processing of forecast data."""
        # Call the processing function with sample data
        processed_data = dust_forecast.process_forecast_data(self.sample_forecast)
        
        # Verify the processed data
        self.assertIn("hourly_forecast", processed_data)
        self.assertIn("daily_average", processed_data)
        self.assertEqual(len(processed_data["hourly_forecast"]), 2)
        
        # Check calculated values
        self.assertGreater(processed_data["daily_average"]["dust_concentration"], 0)
        self.assertGreater(processed_data["max_concentration"], 50)
        self.assertLess(processed_data["min_concentration"], 50)

    def test_get_cleaning_recommendation(self):
        """Test cleaning recommendation logic."""
        # Test high dust scenario
        high_dust = {"dust_concentration": 80.0, "wind_speed": 5.0}
        recommendation = dust_forecast.get_cleaning_recommendation(high_dust)
        self.assertEqual(recommendation["action"], "clean_immediately")
        
        # Test medium dust scenario
        medium_dust = {"dust_concentration": 40.0, "wind_speed": 5.0}
        recommendation = dust_forecast.get_cleaning_recommendation(medium_dust)
        self.assertEqual(recommendation["action"], "schedule_cleaning")
        
        # Test low dust scenario
        low_dust = {"dust_concentration": 10.0, "wind_speed": 5.0}
        recommendation = dust_forecast.get_cleaning_recommendation(low_dust)
        self.assertEqual(recommendation["action"], "no_action_needed")
        
        # Test high wind scenario
        high_wind = {"dust_concentration": 40.0, "wind_speed": 25.0}
        recommendation = dust_forecast.get_cleaning_recommendation(high_wind)
        self.assertEqual(recommendation["action"], "postpone_cleaning")

if __name__ == "__main__":
    unittest.main()

