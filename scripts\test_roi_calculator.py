#!/usr/bin/env python3
"""
Test script for Enhanced ROI Calculator API
Tests all ROI calculation endpoints and validates results
"""

import requests
import json
from datetime import datetime
import sys
import os

# Add backend to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

def test_roi_calculator():
    """Test the ROI Calculator API endpoints"""
    
    base_url = "http://localhost:8000"
    
    print("🧮 Testing Enhanced ROI Calculator API")
    print("=" * 50)
    
    # Test data
    test_request = {
        "system_config": {
            "system_size_mw": 100.0,
            "location": "Riyadh, Saudi Arabia",
            "installation_year": 2024,
            "panel_type": "monocrystalline",
            "inverter_efficiency": 0.96,
            "system_degradation_rate": 0.005
        },
        "environmental_factors": {
            "average_irradiance": 6.2,
            "dust_accumulation_rate": 2.0,
            "rainfall_days_per_year": 15,
            "wind_speed_average": 4.5,
            "humidity_average": 35.0
        },
        "financial_params": {
            "electricity_price_per_kwh": 0.08,
            "electricity_price_escalation": 0.03,
            "discount_rate": 0.08,
            "tax_rate": 0.25,
            "depreciation_years": 10
        },
        "cleaning_costs": {
            "method": "drone_waterless",
            "cost_per_mw": 120.0,
            "frequency_per_year": 12,
            "water_cost_per_liter": 0.001,
            "labor_cost_per_hour": 25.0,
            "equipment_depreciation": 0.1
        },
        "analysis_years": 25
    }
    
    # First, get authentication token (using mock credentials)
    auth_data = {
        "username": "admin",
        "password": "secret"
    }
    
    try:
        print("🔐 Authenticating...")
        auth_response = requests.post(f"{base_url}/api/auth/login", json=auth_data)
        
        if auth_response.status_code == 200:
            token = auth_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Authentication successful")
        else:
            print("❌ Authentication failed - using mock data for testing")
            headers = {}
            
    except requests.exceptions.ConnectionError:
        print("⚠️ API server not running - testing with mock data")
        test_with_mock_data()
        return
    
    # Test 1: Quick ROI Estimate
    print("\n📊 Test 1: Quick ROI Estimate")
    try:
        quick_params = {
            "system_size_mw": 100.0,
            "location": "Riyadh",
            "electricity_price": 0.08,
            "method": "drone_waterless"
        }
        
        response = requests.post(
            f"{base_url}/api/roi/quick-estimate",
            params=quick_params,
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Quick estimate successful")
            print(f"   ROI (5 years): {result['roi_5_years']:.1f}%")
            print(f"   Payback period: {result['payback_period_months']:.1f} months")
            print(f"   Total savings: ${result['total_savings']:,.0f}")
            print(f"   Water saved: {result['water_saved_liters']:,.0f} liters")
        else:
            print(f"❌ Quick estimate failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Quick estimate error: {e}")
    
    # Test 2: Detailed ROI Calculation
    print("\n📈 Test 2: Detailed ROI Calculation")
    try:
        response = requests.post(
            f"{base_url}/api/roi/calculate-detailed",
            json=test_request,
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Detailed calculation successful")
            print(f"   Request ID: {result['request_id']}")
            print(f"   NPV: ${result['net_present_value']:,.0f}")
            print(f"   IRR: {result['internal_rate_of_return']:.2f}%")
            print(f"   ROI (5 years): {result['roi_5_years']:.1f}%")
            print(f"   ROI (25 years): {result['roi_25_years']:.1f}%")
            print(f"   Payback: {result['payback_period_months']:.1f} months")
            print(f"   Monthly data points: {len(result['monthly_data'])}")
            print(f"   Yearly data points: {len(result['yearly_data'])}")
            print(f"   Environmental impact:")
            print(f"     Water saved: {result['water_saved_liters']:,.0f} liters")
            print(f"     Carbon reduced: {result['carbon_reduced_kg']:,.0f} kg CO₂")
            print(f"   Risk analysis:")
            print(f"     Best case ROI: {result['best_case_roi']:.1f}%")
            print(f"     Worst case ROI: {result['worst_case_roi']:.1f}%")
        else:
            print(f"❌ Detailed calculation failed: {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Detailed calculation error: {e}")
    
    # Test 3: Method Comparison
    print("\n🔄 Test 3: Cleaning Method Comparison")
    try:
        response = requests.post(
            f"{base_url}/api/roi/compare-methods",
            json=test_request,
            headers=headers
        )
        
        if response.status_code == 200:
            comparisons = response.json()
            print(f"✅ Method comparison successful")
            print(f"   Compared {len(comparisons)} methods")
            print("\n   Top 3 methods by ROI:")
            
            for i, method in enumerate(comparisons[:3]):
                print(f"   {i+1}. {method['method']}")
                print(f"      ROI (5 years): {method['roi_5_years']:.1f}%")
                print(f"      Payback: {method['payback_period_months']:.1f} months")
                print(f"      Net benefit: ${method['net_benefit_5_years']:,.0f}")
                print(f"      Water usage: {method['water_usage_liters']:,.0f} L")
                print()
        else:
            print(f"❌ Method comparison failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Method comparison error: {e}")
    
    # Test 4: Get Cleaning Method Specifications
    print("\n🛠️ Test 4: Cleaning Method Specifications")
    try:
        response = requests.get(
            f"{base_url}/api/roi/cleaning-methods",
            headers=headers
        )
        
        if response.status_code == 200:
            specs = response.json()
            print(f"✅ Method specifications retrieved")
            print(f"   Total methods: {specs['total_methods']}")
            print(f"   Available methods: {', '.join(specs['methods'])}")
        else:
            print(f"❌ Method specifications failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Method specifications error: {e}")
    
    print("\n🎉 ROI Calculator API testing completed!")

def test_with_mock_data():
    """Test ROI calculator with mock data when API is not available"""
    
    print("\n🧮 Testing ROI Calculator with Mock Data")
    print("=" * 40)
    
    try:
        from app.roi_calculator import roi_calculator, ROIAnalysisRequest
        from app.roi_calculator import SystemConfiguration, EnvironmentalFactors
        from app.roi_calculator import FinancialParameters, CleaningCosts, CleaningMethodROI
        
        # Create test request
        system_config = SystemConfiguration(
            system_size_mw=100.0,
            location="Riyadh, Saudi Arabia",
            installation_year=2024,
            panel_type="monocrystalline"
        )
        
        environmental_factors = EnvironmentalFactors(
            average_irradiance=6.2,
            dust_accumulation_rate=2.0
        )
        
        financial_params = FinancialParameters(
            electricity_price_per_kwh=0.08
        )
        
        cleaning_costs = CleaningCosts(
            method=CleaningMethodROI.DRONE_WATERLESS,
            cost_per_mw=120.0,
            frequency_per_year=12
        )
        
        request = ROIAnalysisRequest(
            system_config=system_config,
            environmental_factors=environmental_factors,
            financial_params=financial_params,
            cleaning_costs=cleaning_costs,
            analysis_years=5
        )
        
        # Test detailed calculation
        print("📈 Testing detailed ROI calculation...")
        result = roi_calculator.calculate_detailed_roi(request)
        
        print(f"✅ Calculation successful!")
        print(f"   ROI (5 years): {result.roi_5_years:.1f}%")
        print(f"   NPV: ${result.net_present_value:,.0f}")
        print(f"   Payback: {result.payback_period_months:.1f} months")
        print(f"   Water saved: {result.water_saved_liters:,.0f} liters")
        print(f"   Carbon reduced: {result.carbon_reduced_kg:,.0f} kg CO₂")
        
        # Test method comparison
        print("\n🔄 Testing method comparison...")
        comparisons = roi_calculator.compare_cleaning_methods(request)
        
        print(f"✅ Comparison successful!")
        print(f"   Compared {len(comparisons)} methods")
        print(f"   Best method: {comparisons[0].method.value}")
        print(f"   Best ROI: {comparisons[0].roi_5_years:.1f}%")
        
        print("\n🎉 Mock testing completed successfully!")
        
    except ImportError as e:
        print(f"❌ Cannot import ROI calculator modules: {e}")
    except Exception as e:
        print(f"❌ Mock testing error: {e}")

if __name__ == "__main__":
    test_roi_calculator()
