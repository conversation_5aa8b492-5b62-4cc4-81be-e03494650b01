#!/usr/bin/env python3
"""
Enhanced Notification System for Solar AI Monitoring
Multi-channel notifications: Email, SMS, Telegram, WhatsApp, Push
"""

import asyncio
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, EmailStr
from datetime import datetime
import logging
import os
import json
import httpx
from twilio.rest import Client as TwilioClient
from telegram import Bot
import aiofiles

logger = logging.getLogger(__name__)

# Notification models
class NotificationChannel(BaseModel):
    type: str  # email, sms, telegram, whatsapp, push
    enabled: bool = True
    config: Dict[str, Any] = {}

class NotificationTemplate(BaseModel):
    template_id: str
    name: str
    subject: str
    body: str
    channels: List[str]
    variables: List[str] = []

class NotificationRecipient(BaseModel):
    user_id: str
    name: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    telegram_chat_id: Optional[str] = None
    whatsapp_number: Optional[str] = None
    push_token: Optional[str] = None
    preferences: Dict[str, bool] = {}

class NotificationRequest(BaseModel):
    template_id: str
    recipients: List[str]  # user_ids
    variables: Dict[str, Any] = {}
    priority: str = "medium"  # low, medium, high, critical
    schedule_time: Optional[datetime] = None

class NotificationLog(BaseModel):
    log_id: str
    template_id: str
    recipient_id: str
    channel: str
    status: str  # sent, failed, pending
    sent_at: datetime
    error_message: Optional[str] = None

# Notification templates
NOTIFICATION_TEMPLATES = {
    "dust_alert": NotificationTemplate(
        template_id="dust_alert",
        name="Dust Level Alert",
        subject="🌪️ High Dust Level Alert - {location}",
        body="""
        High dust levels detected at {location}.
        
        Current dust concentration: {dust_level} μg/m³
        Expected efficiency loss: {efficiency_loss}%
        Recommended action: {recommendation}
        
        Panel IDs affected: {affected_panels}
        
        Please take appropriate cleaning action.
        """,
        channels=["email", "sms", "telegram"],
        variables=["location", "dust_level", "efficiency_loss", "recommendation", "affected_panels"]
    ),
    "cleaning_complete": NotificationTemplate(
        template_id="cleaning_complete",
        name="Cleaning Completed",
        subject="✅ Panel Cleaning Completed - {location}",
        body="""
        Solar panel cleaning has been completed successfully.
        
        Location: {location}
        Cleaning method: {method}
        Panels cleaned: {panel_count}
        Duration: {duration} minutes
        Efficiency improvement: {improvement}%
        
        Next scheduled cleaning: {next_cleaning}
        """,
        channels=["email", "telegram"],
        variables=["location", "method", "panel_count", "duration", "improvement", "next_cleaning"]
    ),
    "system_alert": NotificationTemplate(
        template_id="system_alert",
        name="System Alert",
        subject="⚠️ System Alert - {alert_type}",
        body="""
        System alert detected:
        
        Alert Type: {alert_type}
        Severity: {severity}
        Description: {description}
        Affected Components: {components}
        
        Timestamp: {timestamp}
        
        Please investigate immediately.
        """,
        channels=["email", "sms", "telegram", "push"],
        variables=["alert_type", "severity", "description", "components", "timestamp"]
    ),
    "maintenance_reminder": NotificationTemplate(
        template_id="maintenance_reminder",
        name="Maintenance Reminder",
        subject="🔧 Scheduled Maintenance Reminder",
        body="""
        Scheduled maintenance reminder:
        
        Maintenance Type: {maintenance_type}
        Scheduled Date: {scheduled_date}
        Location: {location}
        Estimated Duration: {duration}
        
        Assigned Technician: {technician}
        
        Please ensure all preparations are completed.
        """,
        channels=["email", "telegram"],
        variables=["maintenance_type", "scheduled_date", "location", "duration", "technician"]
    ),
    "efficiency_report": NotificationTemplate(
        template_id="efficiency_report",
        name="Daily Efficiency Report",
        subject="📊 Daily Solar Panel Efficiency Report",
        body="""
        Daily efficiency report for {date}:
        
        Total Panels: {total_panels}
        Average Efficiency: {avg_efficiency}%
        Energy Generated: {energy_generated} kWh
        Panels Needing Attention: {panels_attention}
        
        Top Performing Panels: {top_panels}
        Underperforming Panels: {underperforming_panels}
        
        Recommended Actions: {recommendations}
        """,
        channels=["email"],
        variables=["date", "total_panels", "avg_efficiency", "energy_generated", "panels_attention", "top_panels", "underperforming_panels", "recommendations"]
    )
}

# Mock recipients database
NOTIFICATION_RECIPIENTS = {
    "admin": NotificationRecipient(
        user_id="admin",
        name="System Administrator",
        email="<EMAIL>",
        phone="+966501234567",
        telegram_chat_id="123456789",
        preferences={
            "email": True,
            "sms": True,
            "telegram": True,
            "push": True
        }
    ),
    "operator": NotificationRecipient(
        user_id="operator",
        name="Solar Farm Operator",
        email="<EMAIL>",
        phone="+966501234568",
        telegram_chat_id="987654321",
        preferences={
            "email": True,
            "sms": False,
            "telegram": True,
            "push": True
        }
    ),
    "technician": NotificationRecipient(
        user_id="technician",
        name="Maintenance Technician",
        email="<EMAIL>",
        phone="+966501234569",
        telegram_chat_id="456789123",
        preferences={
            "email": True,
            "sms": True,
            "telegram": True,
            "push": False
        }
    )
}

class NotificationService:
    """Enhanced notification service with multiple channels"""
    
    def __init__(self):
        self.smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_username = os.getenv("SMTP_USERNAME")
        self.smtp_password = os.getenv("SMTP_PASSWORD")
        
        self.twilio_sid = os.getenv("TWILIO_SID")
        self.twilio_token = os.getenv("TWILIO_TOKEN")
        self.twilio_phone = os.getenv("TWILIO_PHONE")
        
        self.telegram_token = os.getenv("TELEGRAM_BOT_TOKEN")
        
        self.notification_logs: List[NotificationLog] = []

    async def send_notification(self, request: NotificationRequest) -> Dict[str, Any]:
        """Send notification to multiple recipients via multiple channels"""
        
        template = NOTIFICATION_TEMPLATES.get(request.template_id)
        if not template:
            raise ValueError(f"Template {request.template_id} not found")
        
        results = []
        
        for recipient_id in request.recipients:
            recipient = NOTIFICATION_RECIPIENTS.get(recipient_id)
            if not recipient:
                logger.warning(f"Recipient {recipient_id} not found")
                continue
            
            # Send via enabled channels based on recipient preferences
            for channel in template.channels:
                if recipient.preferences.get(channel, False):
                    try:
                        result = await self._send_via_channel(
                            channel, template, recipient, request.variables
                        )
                        results.append({
                            "recipient_id": recipient_id,
                            "channel": channel,
                            "status": "sent" if result else "failed",
                            "timestamp": datetime.now()
                        })
                        
                        # Log notification
                        log_entry = NotificationLog(
                            log_id=f"{recipient_id}_{channel}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                            template_id=request.template_id,
                            recipient_id=recipient_id,
                            channel=channel,
                            status="sent" if result else "failed",
                            sent_at=datetime.now()
                        )
                        self.notification_logs.append(log_entry)
                        
                    except Exception as e:
                        logger.error(f"Failed to send {channel} notification to {recipient_id}: {e}")
                        results.append({
                            "recipient_id": recipient_id,
                            "channel": channel,
                            "status": "failed",
                            "error": str(e),
                            "timestamp": datetime.now()
                        })
        
        return {
            "template_id": request.template_id,
            "total_sent": len([r for r in results if r["status"] == "sent"]),
            "total_failed": len([r for r in results if r["status"] == "failed"]),
            "results": results,
            "sent_at": datetime.now()
        }

    async def _send_via_channel(
        self, 
        channel: str, 
        template: NotificationTemplate, 
        recipient: NotificationRecipient, 
        variables: Dict[str, Any]
    ) -> bool:
        """Send notification via specific channel"""
        
        # Format message with variables
        subject = template.subject.format(**variables)
        body = template.body.format(**variables)
        
        if channel == "email":
            return await self._send_email(recipient.email, subject, body)
        elif channel == "sms":
            return await self._send_sms(recipient.phone, f"{subject}\n\n{body}")
        elif channel == "telegram":
            return await self._send_telegram(recipient.telegram_chat_id, f"*{subject}*\n\n{body}")
        elif channel == "whatsapp":
            return await self._send_whatsapp(recipient.whatsapp_number, f"{subject}\n\n{body}")
        elif channel == "push":
            return await self._send_push_notification(recipient.push_token, subject, body)
        
        return False

    async def _send_email(self, email: str, subject: str, body: str) -> bool:
        """Send email notification"""
        try:
            if not self.smtp_username or not self.smtp_password:
                logger.warning("SMTP credentials not configured")
                return False
            
            message = MIMEMultipart()
            message["From"] = self.smtp_username
            message["To"] = email
            message["Subject"] = subject
            
            message.attach(MIMEText(body, "plain"))
            
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.smtp_username, self.smtp_password)
                server.sendmail(self.smtp_username, email, message.as_string())
            
            logger.info(f"Email sent successfully to {email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {email}: {e}")
            return False

    async def _send_sms(self, phone: str, message: str) -> bool:
        """Send SMS notification via Twilio"""
        try:
            if not self.twilio_sid or not self.twilio_token:
                logger.warning("Twilio credentials not configured")
                return False
            
            client = TwilioClient(self.twilio_sid, self.twilio_token)
            
            message = client.messages.create(
                body=message,
                from_=self.twilio_phone,
                to=phone
            )
            
            logger.info(f"SMS sent successfully to {phone}: {message.sid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send SMS to {phone}: {e}")
            return False

    async def _send_telegram(self, chat_id: str, message: str) -> bool:
        """Send Telegram notification"""
        try:
            if not self.telegram_token:
                logger.warning("Telegram bot token not configured")
                return False
            
            bot = Bot(token=self.telegram_token)
            await bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode='Markdown'
            )
            
            logger.info(f"Telegram message sent successfully to {chat_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Telegram message to {chat_id}: {e}")
            return False

    async def _send_whatsapp(self, phone: str, message: str) -> bool:
        """Send WhatsApp notification via Twilio"""
        try:
            if not self.twilio_sid or not self.twilio_token:
                logger.warning("Twilio credentials not configured")
                return False
            
            client = TwilioClient(self.twilio_sid, self.twilio_token)
            
            message = client.messages.create(
                body=message,
                from_=f"whatsapp:{self.twilio_phone}",
                to=f"whatsapp:{phone}"
            )
            
            logger.info(f"WhatsApp message sent successfully to {phone}: {message.sid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send WhatsApp message to {phone}: {e}")
            return False

    async def _send_push_notification(self, token: str, title: str, body: str) -> bool:
        """Send push notification via Firebase"""
        try:
            # Simulate push notification (implement Firebase FCM in production)
            logger.info(f"Push notification sent to {token}: {title}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send push notification to {token}: {e}")
            return False

    def get_notification_logs(self, limit: int = 100) -> List[NotificationLog]:
        """Get recent notification logs"""
        return sorted(self.notification_logs, key=lambda x: x.sent_at, reverse=True)[:limit]

    def get_notification_stats(self) -> Dict[str, Any]:
        """Get notification statistics"""
        total_sent = len([log for log in self.notification_logs if log.status == "sent"])
        total_failed = len([log for log in self.notification_logs if log.status == "failed"])
        
        channel_stats = {}
        for log in self.notification_logs:
            if log.channel not in channel_stats:
                channel_stats[log.channel] = {"sent": 0, "failed": 0}
            channel_stats[log.channel][log.status] += 1
        
        return {
            "total_notifications": len(self.notification_logs),
            "total_sent": total_sent,
            "total_failed": total_failed,
            "success_rate": (total_sent / len(self.notification_logs) * 100) if self.notification_logs else 0,
            "channel_stats": channel_stats,
            "last_updated": datetime.now()
        }

# Global notification service instance
notification_service = NotificationService()
