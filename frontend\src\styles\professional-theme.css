/* Professional Solar AI Monitoring Theme */

:root {
  /* Solar-inspired color palette */
  --solar-blue-dark: #1e3a8a;
  --solar-blue: #3b82f6;
  --solar-blue-light: #60a5fa;
  --solar-orange: #f59e0b;
  --solar-orange-light: #fbbf24;
  --solar-green: #10b981;
  --solar-green-light: #34d399;
  --solar-red: #ef4444;
  --solar-purple: #8b5cf6;
  
  /* Neutral colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
}

/* Global styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  color: var(--gray-900);
  line-height: 1.6;
}

/* Professional card styles */
.solar-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.solar-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.solar-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--solar-blue-dark) 0%, var(--solar-blue) 100%);
  color: white;
}

.solar-card-content {
  padding: 1.5rem;
}

/* Professional buttons */
.solar-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.solar-btn-primary {
  background: linear-gradient(135deg, var(--solar-blue) 0%, var(--solar-blue-dark) 100%);
  color: white;
  box-shadow: var(--shadow);
}

.solar-btn-primary:hover {
  background: linear-gradient(135deg, var(--solar-blue-dark) 0%, var(--solar-blue) 100%);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.solar-btn-secondary {
  background: white;
  color: var(--solar-blue);
  border: 2px solid var(--solar-blue);
}

.solar-btn-secondary:hover {
  background: var(--solar-blue);
  color: white;
}

.solar-btn-success {
  background: linear-gradient(135deg, var(--solar-green) 0%, var(--solar-green-light) 100%);
  color: white;
}

.solar-btn-warning {
  background: linear-gradient(135deg, var(--solar-orange) 0%, var(--solar-orange-light) 100%);
  color: white;
}

.solar-btn-danger {
  background: linear-gradient(135deg, var(--solar-red) 0%, #dc2626 100%);
  color: white;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-clean {
  background: rgba(16, 185, 129, 0.1);
  color: var(--solar-green);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-dusty {
  background: rgba(245, 158, 11, 0.1);
  color: var(--solar-orange);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-needs-attention {
  background: rgba(239, 68, 68, 0.1);
  color: var(--solar-red);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-offline {
  background: rgba(107, 114, 128, 0.1);
  color: var(--gray-500);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Professional form inputs */
.solar-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  background: white;
}

.solar-input:focus {
  outline: none;
  border-color: var(--solar-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.solar-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Professional metrics display */
.metric-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border-left: 4px solid var(--solar-blue);
  transition: all var(--transition-normal);
}

.metric-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--solar-blue-dark);
  line-height: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
  margin-top: 0.5rem;
}

.metric-change {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.25rem;
}

.metric-change.positive {
  color: var(--solar-green);
}

.metric-change.negative {
  color: var(--solar-red);
}

/* Professional charts */
.chart-container {
  background: white;
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

.chart-subtitle {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: 1.5rem;
}

/* Professional navigation */
.solar-nav {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.solar-nav-item {
  padding: 1rem 1.5rem;
  color: var(--gray-600);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
  border-bottom: 3px solid transparent;
}

.solar-nav-item:hover {
  color: var(--solar-blue);
  background: var(--gray-50);
}

.solar-nav-item.active {
  color: var(--solar-blue);
  border-bottom-color: var(--solar-blue);
}

/* Professional tables */
.solar-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.solar-table th {
  background: var(--gray-50);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--gray-900);
  border-bottom: 1px solid var(--gray-200);
}

.solar-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-700);
}

.solar-table tr:hover {
  background: var(--gray-50);
}

/* Professional alerts */
.solar-alert {
  padding: 1rem 1.5rem;
  border-radius: var(--radius);
  border-left: 4px solid;
  margin-bottom: 1rem;
}

.solar-alert-info {
  background: rgba(59, 130, 246, 0.1);
  border-left-color: var(--solar-blue);
  color: var(--solar-blue-dark);
}

.solar-alert-success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: var(--solar-green);
  color: #065f46;
}

.solar-alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: var(--solar-orange);
  color: #92400e;
}

.solar-alert-error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: var(--solar-red);
  color: #991b1b;
}

/* Professional loading states */
.solar-loading {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--gray-300);
  border-top: 2px solid var(--solar-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.solar-skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-300) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Responsive design */
@media (max-width: 768px) {
  .solar-card {
    margin: 0.5rem;
    border-radius: var(--radius);
  }
  
  .solar-card-header,
  .solar-card-content {
    padding: 1rem;
  }
  
  .metric-value {
    font-size: 1.5rem;
  }
  
  .solar-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-900: #f9fafb;
  }
  
  body {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: var(--gray-900);
  }
  
  .solar-card {
    background: #374151;
    border-color: #4b5563;
  }
  
  .solar-input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
}

/* Print styles */
@media print {
  .solar-card {
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }
  
  .solar-btn {
    display: none;
  }
  
  .chart-container {
    break-inside: avoid;
  }
}
