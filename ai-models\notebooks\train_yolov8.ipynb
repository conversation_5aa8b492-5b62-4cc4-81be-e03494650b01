# train_yolov8.ipynb

This notebook demonstrates the process of fine-tuning a YOLOv8 model for dust and crack detection on solar panels.

## 1. Setup and Installation

First, ensure you have the necessary libraries installed. If running in Google Colab, some of these might be pre-installed.

```bash
pip install ultralytics
```

## 2. Data Preparation

For this project, we assume you have a dataset of 300 images of solar panels with annotations for dust and cracks. The annotations should be in YOLO format.

Your dataset directory structure should look something like this:

```
dataset/
├── images/
│   ├── train/
│   │   ├── image1.jpg
│   │   └── ...
│   ├── val/
│   │   ├── image_val1.jpg
│   │   └── ...
│   └── test/
│       ├── image_test1.jpg
│       └── ...
└── labels/
    ├── train/
    │   ├── image1.txt
    │   └── ...
    ├── val/
    │   ├── image_val1.txt
    │   └── ...
    └── test/
        ├── image_test1.txt
        └── ...
```

You also need a `dataset.yaml` file that points to your data and defines your classes:

```yaml
train: ../dataset/images/train
val: ../dataset/images/val

nc: 2  # number of classes (e.g., dust, crack)
names: ['dust', 'crack']
```

## 3. Load YOLOv8 Model

We will load a pre-trained YOLOv8s model and fine-tune it.

```python
from ultralytics import YOLO

# Load a pre-trained YOLOv8s model
model = YOLO('yolov8s.pt')

print("YOLOv8 model loaded successfully.")
```

## 4. Fine-tuning the Model

Now, we will train the model using your custom dataset. Adjust the `epochs`, `batch`, and `imgsz` parameters as needed.

```python
# Train the model
results = model.train(data='dataset.yaml', epochs=100, imgsz=640, batch=16)

print("Model fine-tuning complete.")
```

## 5. Evaluate the Model

After training, evaluate the model's performance on the validation set.

```python
# Evaluate the model on the validation set
metrics = model.val()

print("Model evaluation results:")
print(f"mAP50-95: {metrics.box.map}")
print(f"mAP50: {metrics.box.map50}")
print(f"mAP75: {metrics.box.map75}")
```

## 6. Run Inference (Optional)

You can use the trained model to make predictions on new images.

```python
# Run inference on a sample image
# Replace 'path/to/your/image.jpg' with an actual image from your test set
results = model('path/to/your/image.jpg')

# Show results
for r in results:
    im_array = r.plot()  # plot a BGR numpy array of predictions
    im = Image.fromarray(im_array[..., ::-1])  # RGB PIL image
    im.show()  # show image
    im.save('results.jpg')  # save image

print("Inference complete. Results saved to results.jpg")
```

## 7. Export Model (Optional)

You can export the trained model to various formats for deployment.

```python
# Export the model to ONNX format
model.export(format='onnx')

print("Model exported to ONNX format.")
```


