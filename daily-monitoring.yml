name: Daily Solar Panel Monitoring

on:
  schedule:
    # Run daily at 6:00 AM UTC (9:00 AM Riyadh time)
    - cron: '0 6 * * *'
  workflow_dispatch:  # Allow manual triggering

jobs:
  monitor-and-process:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Download CAMS dust data
      env:
        CAMS_API_KEY: ${{ secrets.CAMS_API_KEY }}
      run: |
        python scripts/download_cams_data.py
        
    - name: Process dust forecast
      run: |
        python cams_to_json.py
        
    - name: Check panel production
      env:
        TWILIO_ACCOUNT_SID: ${{ secrets.TWILIO_ACCOUNT_SID }}
        TWILIO_AUTH_TOKEN: ${{ secrets.TWILIO_AUTH_TOKEN }}
      run: |
        python notify.py --check-production
        
    - name: Update battery schedule
      run: |
        python battery_scheduler.py
        
    - name: Commit and push results
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add dust_riyadh.json cleaning_schedule.json
        git diff --staged --quiet || git commit -m "Daily update: dust forecast and cleaning schedule $(date)"
        git push
        
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: daily-monitoring-data
        path: |
          dust_riyadh.json
          cleaning_schedule.json
        retention-days: 30

