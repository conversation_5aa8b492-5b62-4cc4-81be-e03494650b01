version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: solar-postgres
    environment:
      POSTGRES_DB: solar_monitoring
      POSTGRES_USER: solar_user
      POSTGRES_PASSWORD: solar_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - solar-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: solar-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - solar-network
    restart: unless-stopped

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: solar-backend
    environment:
      - DATABASE_URL=****************************************************/solar_monitoring
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-here
      - ENVIRONMENT=production
    volumes:
      - ./backend:/app
      - ./model:/app/model
      - ./scripts:/app/scripts
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - solar-network
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: solar-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - solar-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: solar-nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - backend
    networks:
      - solar-network
    restart: unless-stopped

  # Celery Worker for Background Tasks
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: solar-celery-worker
    environment:
      - DATABASE_URL=****************************************************/solar_monitoring
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./backend:/app
      - ./model:/app/model
      - ./scripts:/app/scripts
    depends_on:
      - postgres
      - redis
    networks:
      - solar-network
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: solar-celery-beat
    environment:
      - DATABASE_URL=****************************************************/solar_monitoring
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./backend:/app
      - ./model:/app/model
      - ./scripts:/app/scripts
    depends_on:
      - postgres
      - redis
    networks:
      - solar-network
    restart: unless-stopped
    command: celery -A app.celery beat --loglevel=info

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: solar-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - solar-network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: solar-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - solar-network
    restart: unless-stopped

  # InfluxDB for Time Series Data
  influxdb:
    image: influxdb:2.7-alpine
    container_name: solar-influxdb
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=password
      - DOCKER_INFLUXDB_INIT_ORG=solar-monitoring
      - DOCKER_INFLUXDB_INIT_BUCKET=sensor-data
    volumes:
      - influxdb_data:/var/lib/influxdb2
    ports:
      - "8086:8086"
    networks:
      - solar-network
    restart: unless-stopped

  # MQTT Broker for IoT
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: solar-mosquitto
    volumes:
      - ./mosquitto/config:/mosquitto/config
      - ./mosquitto/data:/mosquitto/data
      - ./mosquitto/log:/mosquitto/log
    ports:
      - "1883:1883"
      - "9001:9001"
    networks:
      - solar-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  influxdb_data:

networks:
  solar-network:
    driver: bridge
