# Project Restructure Plan

## New Folder Structure

```
Solar-AI-Cleaning-Monitoring/
├── frontend/                    # React Web Application
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   └── styles/
│   ├── public/
│   ├── mobile/                  # React Native Mobile App
│   │   ├── src/
│   │   ├── android/
│   │   ├── ios/
│   │   └── package.json
│   ├── package.json
│   ├── vite.config.js
│   └── tailwind.config.js
│
├── backend/                     # FastAPI Backend
│   ├── app/
│   │   ├── api/
│   │   ├── models/
│   │   ├── services/
│   │   └── utils/
│   ├── main.py
│   ├── requirements.txt
│   └── Dockerfile
│
├── scripts/                     # Automation Scripts
│   ├── data_processing/
│   │   ├── cams_to_json.py
│   │   ├── download_cams_data.py
│   │   └── dust_forecast.py
│   ├── monitoring/
│   │   ├── daily_monitoring.sh
│   │   ├── battery_scheduler.py
│   │   └── health_check.py
│   ├── notifications/
│   │   ├── notify.py
│   │   ├── email_sender.py
│   │   └── sms_sender.py
│   └── utilities/
│       ├── create_roi_calculator.py
│       └── test_api.py
│
├── model/                       # AI/ML Models
│   ├── yolo/
│   │   ├── weights/
│   │   ├── configs/
│   │   └── inference.py
│   ├── thermal/
│   │   ├── hotspot_detection.py
│   │   └── thermal_analysis.py
│   ├── datasets/
│   ├── notebooks/
│   │   └── train_yolov8.ipynb
│   └── requirements.txt
│
├── docs/                        # Documentation
│   ├── api/
│   ├── user_guides/
│   ├── technical/
│   ├── business/
│   ├── README.md
│   ├── COMPETITION_SUBMISSION_CHECKLIST.md
│   └── mobile_app_proposal.md
│
├── slides/                      # Presentations
│   ├── solar_presentation_arabic.html
│   ├── solar_presentation_arabic.pptx
│   ├── video_script_1_minute.md
│   └── assets/
│
├── docker-compose.yml
├── .gitignore
└── LICENSE
```

## File Movement Plan

### Frontend Files
- Move: frontend/src/* → frontend/src/
- Move: mobile-app/* → frontend/mobile/
- Keep: package.json, vite.config.js, tailwind.config.js

### Backend Files  
- Move: backend/app/main.py → backend/main.py
- Keep: backend/requirements.txt
- Add: New API endpoints for cleaning methods

### Scripts
- Move: scripts/data_processing/* → scripts/data_processing/
- Move: scripts/monitoring/* → scripts/monitoring/
- Move: scripts/notifications/* → scripts/notifications/
- Move: scripts/utilities/* → scripts/utilities/

### Models
- Move: ai-models/* → model/
- Move: train_yolov8.ipynb → model/notebooks/

### Documentation
- Move: documentation/* → docs/
- Move: *.md files → docs/
- Create: mobile_app_proposal.md

### Presentations
- Move: presentations/* → slides/
- Create: solar_presentation_arabic.pptx
- Update: HTML slides with new cleaning methods

## New Features to Add

### Extended Cleaning Methods
1. Crawler Robots
2. Self-Cleaning Nano-Coatings  
3. Air Blowers
4. Ultrasonic Vibrations
5. Electrostatic Cleaning (NEW)
6. UV Surface Cleaning (NEW)
7. Predictive Maintenance Logic
8. IoT Sensor Integration

### API Enhancements
- `/api/select-method` - Choose cleaning method
- `/api/predict-schedule` - Predictive scheduling
- `/api/sensors/live` - Live sensor data
- `/api/roi/calculate` - Enhanced ROI calculation

### Dashboard Features
- Method Selector Widget
- Live Sensor Widgets (temp, humidity, voltage)
- Gantt Chart for Cleaning Schedule
- Enhanced ROI Charts

### Mobile App Features
- Real-time sensor monitoring
- Method selection interface
- Push notifications for maintenance
- Offline mode with sync
