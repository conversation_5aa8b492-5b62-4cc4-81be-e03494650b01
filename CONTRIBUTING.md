# Contributing to Solar AI Cleaning & Monitoring System

Thank you for your interest in contributing to the Solar AI Cleaning & Monitoring System! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)
- [Development Standards](#development-standards)
- [Testing](#testing)
- [Documentation](#documentation)
- [Community](#community)

## Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow. Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md) to help us maintain a welcoming and inclusive community.

### Our Standards

- **Be respectful**: Treat all community members with respect and kindness
- **Be inclusive**: Welcome newcomers and help them get started
- **Be collaborative**: Work together to improve the project
- **Be constructive**: Provide helpful feedback and suggestions
- **Be professional**: Maintain professional communication standards

## Getting Started

### Prerequisites

Before contributing, ensure you have:

- **Python 3.8+** installed
- **Node.js 16+** installed
- **Git** for version control
- **Docker** (optional, for containerized development)
- **Basic knowledge** of React, FastAPI, and AI/ML concepts

### Fork and Clone

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/your-username/solar-ai-monitoring.git
   cd solar-ai-monitoring
   ```
3. **Add upstream remote**:
   ```bash
   git remote add upstream https://github.com/original-repo/solar-ai-monitoring.git
   ```

## Development Setup

### Backend Setup

1. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the backend**:
   ```bash
   python app/main.py
   ```

### Frontend Setup

1. **Install dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

### Mobile App Setup

1. **Install dependencies**:
   ```bash
   cd mobile-app
   npm install
   ```

2. **Start Metro bundler**:
   ```bash
   npm start
   ```

## Contributing Guidelines

### Types of Contributions

We welcome various types of contributions:

- **Bug fixes**: Fix issues and improve stability
- **Feature development**: Add new functionality
- **Documentation**: Improve or add documentation
- **Testing**: Add or improve test coverage
- **Performance**: Optimize code performance
- **UI/UX**: Improve user interface and experience
- **Translations**: Add or improve language support

### Contribution Workflow

1. **Check existing issues** to avoid duplicate work
2. **Create an issue** for new features or significant changes
3. **Discuss the approach** with maintainers before starting
4. **Create a feature branch** from the main branch
5. **Make your changes** following our coding standards
6. **Write tests** for new functionality
7. **Update documentation** as needed
8. **Submit a pull request** with clear description

### Branch Naming Convention

Use descriptive branch names:
- `feature/add-new-cleaning-method`
- `bugfix/fix-dashboard-loading`
- `docs/update-api-documentation`
- `test/add-unit-tests-for-sensors`

## Pull Request Process

### Before Submitting

1. **Sync with upstream**:
   ```bash
   git fetch upstream
   git checkout main
   git merge upstream/main
   ```

2. **Run tests**:
   ```bash
   # Backend tests
   cd backend && python -m pytest
   
   # Frontend tests
   cd frontend && npm test
   ```

3. **Check code quality**:
   ```bash
   # Python linting
   flake8 backend/
   black backend/
   
   # JavaScript linting
   cd frontend && npm run lint
   ```

### Pull Request Template

When submitting a PR, include:

- **Clear title** describing the change
- **Detailed description** of what was changed and why
- **Related issue** number (if applicable)
- **Testing instructions** for reviewers
- **Screenshots** for UI changes
- **Breaking changes** documentation (if any)

### Review Process

1. **Automated checks** must pass (CI/CD pipeline)
2. **Code review** by at least one maintainer
3. **Testing** by reviewers when applicable
4. **Documentation review** for significant changes
5. **Approval** and merge by maintainers

## Issue Reporting

### Bug Reports

When reporting bugs, include:

- **Clear title** and description
- **Steps to reproduce** the issue
- **Expected vs actual behavior**
- **Environment details** (OS, Python version, etc.)
- **Screenshots or logs** if applicable
- **Minimal code example** demonstrating the issue

### Feature Requests

For feature requests, provide:

- **Clear description** of the proposed feature
- **Use case** and motivation
- **Proposed implementation** approach
- **Potential impact** on existing functionality
- **Alternative solutions** considered

### Issue Labels

We use labels to categorize issues:
- `bug`: Something isn't working
- `enhancement`: New feature or request
- `documentation`: Improvements or additions to docs
- `good first issue`: Good for newcomers
- `help wanted`: Extra attention is needed
- `priority-high`: High priority issue

## Development Standards

### Python Code Standards

- **Follow PEP 8** style guidelines
- **Use type hints** for function parameters and returns
- **Write docstrings** for all public functions and classes
- **Use meaningful variable names**
- **Keep functions small** and focused
- **Handle exceptions** appropriately

Example:
```python
def calculate_dust_level(image_path: str) -> float:
    """
    Calculate dust level from solar panel image.
    
    Args:
        image_path: Path to the solar panel image
        
    Returns:
        Dust level as percentage (0-100)
        
    Raises:
        FileNotFoundError: If image file doesn't exist
        ValueError: If image format is invalid
    """
    # Implementation here
    pass
```

### JavaScript/React Standards

- **Use ESLint** configuration provided
- **Follow React best practices**
- **Use functional components** with hooks
- **Write meaningful component names**
- **Use TypeScript** for type safety (when applicable)
- **Follow accessibility guidelines**

Example:
```jsx
import React, { useState, useEffect } from 'react';

const PanelStatusCard = ({ panelId, onStatusUpdate }) => {
  const [status, setStatus] = useState(null);
  
  useEffect(() => {
    // Fetch panel status
  }, [panelId]);
  
  return (
    <div className="panel-status-card">
      {/* Component content */}
    </div>
  );
};

export default PanelStatusCard;
```

### Commit Message Format

Use conventional commit format:
```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
- `feat(api): add new cleaning method endpoint`
- `fix(dashboard): resolve panel status loading issue`
- `docs(readme): update installation instructions`

## Testing

### Test Requirements

- **Unit tests** for all new functions
- **Integration tests** for API endpoints
- **Component tests** for React components
- **End-to-end tests** for critical user flows

### Running Tests

```bash
# Backend tests
cd backend
python -m pytest tests/ -v

# Frontend tests
cd frontend
npm test

# E2E tests
npm run test:e2e
```

### Test Coverage

- Maintain **>80% code coverage**
- Include **edge cases** in tests
- Test **error conditions**
- Mock **external dependencies**

## Documentation

### Documentation Types

- **API Documentation**: OpenAPI/Swagger specs
- **User Guides**: Step-by-step instructions
- **Technical Documentation**: Architecture and design
- **Code Comments**: Inline documentation

### Documentation Standards

- **Clear and concise** writing
- **Include examples** where helpful
- **Keep up-to-date** with code changes
- **Use proper formatting** (Markdown)
- **Include diagrams** for complex concepts

## Community

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Email**: <EMAIL>
- **Documentation**: https://docs.solar-ai-monitoring.com

### Getting Help

- **Check existing documentation** first
- **Search existing issues** for similar problems
- **Ask questions** in GitHub Discussions
- **Be specific** about your problem or question
- **Provide context** and relevant details

### Recognition

Contributors are recognized through:
- **Contributors list** in README
- **Release notes** acknowledgments
- **GitHub contributor graphs**
- **Special recognition** for significant contributions

## License

By contributing to this project, you agree that your contributions will be licensed under the same [MIT License](LICENSE) that covers the project.

---

Thank you for contributing to the Solar AI Cleaning & Monitoring System! Your contributions help make solar energy more efficient and sustainable. 🌞
