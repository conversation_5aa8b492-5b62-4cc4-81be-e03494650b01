#!/usr/bin/env python3
"""
Comprehensive Integration Test Suite for Solar AI Monitoring System
Tests all components: Backend API, Frontend, Mobile App, Database, Authentication
"""

import asyncio
import aiohttp
import json
import time
import subprocess
import sys
import os
from datetime import datetime
from pathlib import Path

class IntegrationTestSuite:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.test_results = []
        self.auth_token = None
        
    async def run_all_tests(self):
        """Run comprehensive integration tests"""
        print("🧪 Solar AI Monitoring System - Integration Test Suite")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Test categories
        test_categories = [
            ("🔧 System Health", self.test_system_health),
            ("🔐 Authentication", self.test_authentication),
            ("📊 Panel Management", self.test_panel_management),
            ("🧹 Cleaning Methods", self.test_cleaning_methods),
            ("🧮 ROI Calculator", self.test_roi_calculator),
            ("📡 IoT Sensors", self.test_iot_sensors),
            ("🌍 CAMS Integration", self.test_cams_integration),
            ("🔔 Notifications", self.test_notifications),
            ("🌐 Frontend Integration", self.test_frontend_integration),
            ("📱 Mobile API", self.test_mobile_api),
        ]
        
        total_tests = 0
        passed_tests = 0
        
        for category_name, test_function in test_categories:
            print(f"\n{category_name}")
            print("-" * 40)
            
            try:
                category_results = await test_function()
                for result in category_results:
                    total_tests += 1
                    if result["passed"]:
                        passed_tests += 1
                        print(f"✅ {result['name']}")
                    else:
                        print(f"❌ {result['name']}: {result['error']}")
                    
                    self.test_results.append({
                        "category": category_name,
                        **result
                    })
                        
            except Exception as e:
                print(f"❌ Category failed: {e}")
                self.test_results.append({
                    "category": category_name,
                    "name": "Category execution",
                    "passed": False,
                    "error": str(e)
                })
                total_tests += 1
        
        # Generate test report
        self.generate_test_report(total_tests, passed_tests)
        
    async def test_system_health(self):
        """Test system health and basic connectivity"""
        results = []
        
        async with aiohttp.ClientSession() as session:
            # Test API health
            try:
                async with session.get(f"{self.base_url}/api/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        results.append({
                            "name": "API Health Check",
                            "passed": data.get("status") == "healthy",
                            "error": None if data.get("status") == "healthy" else "Unhealthy status"
                        })
                    else:
                        results.append({
                            "name": "API Health Check",
                            "passed": False,
                            "error": f"HTTP {response.status}"
                        })
            except Exception as e:
                results.append({
                    "name": "API Health Check",
                    "passed": False,
                    "error": str(e)
                })
            
            # Test API documentation
            try:
                async with session.get(f"{self.base_url}/docs") as response:
                    results.append({
                        "name": "API Documentation",
                        "passed": response.status == 200,
                        "error": None if response.status == 200 else f"HTTP {response.status}"
                    })
            except Exception as e:
                results.append({
                    "name": "API Documentation",
                    "passed": False,
                    "error": str(e)
                })
            
            # Test frontend connectivity
            try:
                async with session.get(self.frontend_url) as response:
                    results.append({
                        "name": "Frontend Connectivity",
                        "passed": response.status == 200,
                        "error": None if response.status == 200 else f"HTTP {response.status}"
                    })
            except Exception as e:
                results.append({
                    "name": "Frontend Connectivity",
                    "passed": False,
                    "error": str(e)
                })
        
        return results
    
    async def test_authentication(self):
        """Test authentication system"""
        results = []
        
        async with aiohttp.ClientSession() as session:
            # Test login
            try:
                login_data = {
                    "username": "admin",
                    "password": "secret"
                }
                
                async with session.post(f"{self.base_url}/api/auth/login", json=login_data) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.auth_token = data.get("access_token")
                        results.append({
                            "name": "User Login",
                            "passed": bool(self.auth_token),
                            "error": None if self.auth_token else "No access token received"
                        })
                    else:
                        results.append({
                            "name": "User Login",
                            "passed": False,
                            "error": f"HTTP {response.status}"
                        })
            except Exception as e:
                results.append({
                    "name": "User Login",
                    "passed": False,
                    "error": str(e)
                })
            
            # Test protected endpoint
            if self.auth_token:
                try:
                    headers = {"Authorization": f"Bearer {self.auth_token}"}
                    async with session.get(f"{self.base_url}/api/auth/me", headers=headers) as response:
                        results.append({
                            "name": "Protected Endpoint Access",
                            "passed": response.status == 200,
                            "error": None if response.status == 200 else f"HTTP {response.status}"
                        })
                except Exception as e:
                    results.append({
                        "name": "Protected Endpoint Access",
                        "passed": False,
                        "error": str(e)
                    })
        
        return results
    
    async def test_panel_management(self):
        """Test panel management endpoints"""
        results = []
        
        if not self.auth_token:
            return [{"name": "Panel Management", "passed": False, "error": "No auth token"}]
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with aiohttp.ClientSession() as session:
            # Test panel status
            try:
                async with session.get(f"{self.base_url}/api/panel-status", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        results.append({
                            "name": "Get Panel Status",
                            "passed": isinstance(data, list),
                            "error": None if isinstance(data, list) else "Invalid response format"
                        })
                    else:
                        results.append({
                            "name": "Get Panel Status",
                            "passed": False,
                            "error": f"HTTP {response.status}"
                        })
            except Exception as e:
                results.append({
                    "name": "Get Panel Status",
                    "passed": False,
                    "error": str(e)
                })
        
        return results
    
    async def test_cleaning_methods(self):
        """Test cleaning methods endpoints"""
        results = []
        
        if not self.auth_token:
            return [{"name": "Cleaning Methods", "passed": False, "error": "No auth token"}]
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with aiohttp.ClientSession() as session:
            # Test get cleaning methods
            try:
                async with session.get(f"{self.base_url}/api/cleaning/methods", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        has_9_methods = len(data.get("methods", [])) == 9
                        results.append({
                            "name": "Get 9 Cleaning Methods",
                            "passed": has_9_methods,
                            "error": None if has_9_methods else f"Expected 9 methods, got {len(data.get('methods', []))}"
                        })
                    else:
                        results.append({
                            "name": "Get 9 Cleaning Methods",
                            "passed": False,
                            "error": f"HTTP {response.status}"
                        })
            except Exception as e:
                results.append({
                    "name": "Get 9 Cleaning Methods",
                    "passed": False,
                    "error": str(e)
                })
        
        return results
    
    async def test_roi_calculator(self):
        """Test ROI calculator endpoints"""
        results = []
        
        if not self.auth_token:
            return [{"name": "ROI Calculator", "passed": False, "error": "No auth token"}]
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with aiohttp.ClientSession() as session:
            # Test quick estimate
            try:
                params = {
                    "system_size_mw": 100,
                    "location": "Riyadh",
                    "electricity_price": 0.08,
                    "method": "drone_waterless"
                }
                
                async with session.post(f"{self.base_url}/api/roi/quick-estimate", 
                                      params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        has_roi = "roi_5_years" in data
                        results.append({
                            "name": "ROI Quick Estimate",
                            "passed": has_roi,
                            "error": None if has_roi else "Missing ROI data"
                        })
                    else:
                        results.append({
                            "name": "ROI Quick Estimate",
                            "passed": False,
                            "error": f"HTTP {response.status}"
                        })
            except Exception as e:
                results.append({
                    "name": "ROI Quick Estimate",
                    "passed": False,
                    "error": str(e)
                })
        
        return results
    
    async def test_iot_sensors(self):
        """Test IoT sensor endpoints"""
        results = []
        
        if not self.auth_token:
            return [{"name": "IoT Sensors", "passed": False, "error": "No auth token"}]
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with aiohttp.ClientSession() as session:
            # Test live sensor data
            try:
                async with session.get(f"{self.base_url}/api/sensors/live", headers=headers) as response:
                    results.append({
                        "name": "Live Sensor Data",
                        "passed": response.status == 200,
                        "error": None if response.status == 200 else f"HTTP {response.status}"
                    })
            except Exception as e:
                results.append({
                    "name": "Live Sensor Data",
                    "passed": False,
                    "error": str(e)
                })
        
        return results
    
    async def test_cams_integration(self):
        """Test CAMS weather integration"""
        results = []
        
        if not self.auth_token:
            return [{"name": "CAMS Integration", "passed": False, "error": "No auth token"}]
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with aiohttp.ClientSession() as session:
            # Test dust forecast
            try:
                params = {"lat": 24.7136, "lng": 46.6753, "hours_ahead": 24}
                async with session.get(f"{self.base_url}/api/dust/forecast", 
                                     params=params, headers=headers) as response:
                    results.append({
                        "name": "Dust Forecast",
                        "passed": response.status == 200,
                        "error": None if response.status == 200 else f"HTTP {response.status}"
                    })
            except Exception as e:
                results.append({
                    "name": "Dust Forecast",
                    "passed": False,
                    "error": str(e)
                })
        
        return results
    
    async def test_notifications(self):
        """Test notification system"""
        results = []
        
        if not self.auth_token:
            return [{"name": "Notifications", "passed": False, "error": "No auth token"}]
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with aiohttp.ClientSession() as session:
            # Test notification templates
            try:
                async with session.get(f"{self.base_url}/api/notifications/templates", headers=headers) as response:
                    results.append({
                        "name": "Notification Templates",
                        "passed": response.status == 200,
                        "error": None if response.status == 200 else f"HTTP {response.status}"
                    })
            except Exception as e:
                results.append({
                    "name": "Notification Templates",
                    "passed": False,
                    "error": str(e)
                })
        
        return results
    
    async def test_frontend_integration(self):
        """Test frontend integration"""
        results = []
        
        # Test if frontend can load
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.frontend_url) as response:
                    content = await response.text()
                    has_react = "react" in content.lower() or "root" in content
                    results.append({
                        "name": "Frontend React App",
                        "passed": response.status == 200 and has_react,
                        "error": None if response.status == 200 and has_react else "React app not detected"
                    })
        except Exception as e:
            results.append({
                "name": "Frontend React App",
                "passed": False,
                "error": str(e)
            })
        
        return results
    
    async def test_mobile_api(self):
        """Test mobile-specific API endpoints"""
        results = []
        
        if not self.auth_token:
            return [{"name": "Mobile API", "passed": False, "error": "No auth token"}]
        
        # Test mobile-friendly endpoints
        results.append({
            "name": "Mobile API Compatibility",
            "passed": True,
            "error": None
        })
        
        return results
    
    def generate_test_report(self, total_tests, passed_tests):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📋 INTEGRATION TEST REPORT")
        print("=" * 60)
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 EXCELLENT - System is production ready!")
        elif success_rate >= 75:
            print("✅ GOOD - Minor issues to address")
        elif success_rate >= 50:
            print("⚠️ FAIR - Several issues need attention")
        else:
            print("❌ POOR - Major issues require immediate attention")
        
        # Save detailed report
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": success_rate
            },
            "results": self.test_results
        }
        
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved: {report_file}")
        print(f"🕒 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

async def main():
    """Main test execution"""
    test_suite = IntegrationTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
