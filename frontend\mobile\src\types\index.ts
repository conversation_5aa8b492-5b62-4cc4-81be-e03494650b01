// Type definitions for Solar Monitoring App

export interface PanelLocation {
  lat: number;
  lng: number;
}

export interface PanelStatus {
  panel_id: string;
  location: PanelLocation;
  status: 'clean' | 'dusty' | 'needs_attention' | 'offline';
  dust_level: number;
  efficiency: number;
  last_cleaned: string;
  defects: string[];
  battery_level?: number;
  temperature?: number;
}

export interface SystemStats {
  total_panels: number;
  active_panels: number;
  panels_needing_cleaning: number;
  average_efficiency: number;
  average_battery_level: number;
  last_updated: string;
}

export interface DustData {
  timestamp: string;
  dust_level: number;
  location: PanelLocation;
  weather_conditions: {
    temperature: number;
    humidity: number;
    wind_speed: number;
    wind_direction: number;
  };
}

export interface Notification {
  id: string;
  type: 'production_drop' | 'maintenance_required' | 'cleaning_completed' | 'weather_alert';
  title: string;
  message: string;
  panel_id?: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface ROICalculation {
  system_size_mw: number;
  current_efficiency_loss: number;
  electricity_price_per_kwh: number;
  cleaning_frequency_per_year: number;
  maintenance_cost_per_cleaning: number;
  annual_energy_loss_kwh: number;
  annual_revenue_loss: number;
  annual_cleaning_cost: number;
  net_annual_savings: number;
  payback_period_months: number;
  five_year_roi: number;
}

export interface WeatherData {
  temperature: number;
  humidity: number;
  wind_speed: number;
  wind_direction: number;
  visibility: number;
  dust_forecast: {
    current: number;
    next_24h: number[];
    next_7d: number[];
  };
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'operator' | 'viewer';
  preferences: {
    language: 'en' | 'ar';
    notifications_enabled: boolean;
    refresh_interval: number;
    theme: 'light' | 'dark';
  };
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

export interface PanelsState {
  panels: PanelStatus[];
  selectedPanel: PanelStatus | null;
  stats: SystemStats | null;
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

export interface DustDataState {
  current: DustData | null;
  historical: DustData[];
  forecast: number[];
  loading: boolean;
  error: string | null;
}

export interface NotificationsState {
  notifications: Notification[];
  unreadCount: number;
  settings: {
    push_enabled: boolean;
    email_enabled: boolean;
    sms_enabled: boolean;
    categories: {
      production_drop: boolean;
      maintenance_required: boolean;
      weather_alerts: boolean;
    };
  };
  loading: boolean;
  error: string | null;
}

export interface SettingsState {
  language: 'en' | 'ar';
  theme: 'light' | 'dark';
  refresh_interval: number;
  notifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
  };
  data_usage: {
    wifi_only: boolean;
    auto_sync: boolean;
  };
}

export interface AppState {
  auth: AuthState;
  panels: PanelsState;
  dustData: DustDataState;
  notifications: NotificationsState;
  settings: SettingsState;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Navigation types
export type RootStackParamList = {
  Main: undefined;
  Login: undefined;
  PanelDetails: { panelId: string };
  Settings: undefined;
  Notifications: undefined;
  ROICalculator: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  RealTimeData: undefined;
  Panels: undefined;
  Notifications: undefined;
  More: undefined;
};

// Chart data types
export interface ChartDataPoint {
  x: string | number;
  y: number;
  label?: string;
}

export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    color?: (opacity: number) => string;
    strokeWidth?: number;
  }[];
}

// WebSocket message types
export interface WebSocketMessage {
  type: 'dust_update' | 'panel_status' | 'notification' | 'system_stats';
  data: any;
  timestamp: string;
}

// Error types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface ValidationError {
  field: string;
  message: string;
}

// Form types
export interface LoginForm {
  username: string;
  password: string;
}

export interface ROIForm {
  system_size_mw: number;
  current_efficiency_loss: number;
  electricity_price_per_kwh: number;
  cleaning_frequency_per_year: number;
  maintenance_cost_per_cleaning: number;
}

export interface NotificationSettings {
  push_enabled: boolean;
  email_enabled: boolean;
  sms_enabled: boolean;
  production_drop: boolean;
  maintenance_required: boolean;
  weather_alerts: boolean;
}

// Component props types
export interface PanelCardProps {
  panel: PanelStatus;
  onPress: (panel: PanelStatus) => void;
}

export interface ChartComponentProps {
  data: ChartData;
  title: string;
  height?: number;
  showGrid?: boolean;
  animated?: boolean;
}

export interface StatCardProps {
  title: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  color?: string;
  icon?: string;
}
