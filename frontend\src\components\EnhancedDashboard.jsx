import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from './ui/tabs';
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar, 
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell
} from 'recharts';
import { 
  Sun, 
  Zap, 
  Droplets, 
  Wind, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  TrendingDown,
  Activity,
  Bell,
  Settings,
  Download,
  RefreshCw,
  MapPin,
  Calendar,
  DollarSign
} from 'lucide-react';
import MethodSelector from './MethodSelector';
import LiveSensorWidgets from './LiveSensorWidgets';

const EnhancedDashboard = () => {
  const [panelData, setPanelData] = useState([]);
  const [dustForecast, setDustForecast] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [selectedPanels, setSelectedPanels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  useEffect(() => {
    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch panel status
      const panelResponse = await fetch('/api/panel-status');
      const panels = await panelResponse.json();
      setPanelData(panels);
      
      // Fetch dust forecast for first panel location
      if (panels.length > 0) {
        const firstPanel = panels[0];
        const dustResponse = await fetch(
          `/api/dust/forecast?lat=${firstPanel.location.lat}&lng=${firstPanel.location.lng}&hours_ahead=72`
        );
        const dustData = await dustResponse.json();
        setDustForecast(dustData.forecasts || []);
      }
      
      // Fetch notifications
      const notifResponse = await fetch('/api/notifications/logs?limit=10');
      const notifData = await notifResponse.json();
      setNotifications(notifData.logs || []);
      
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'clean': return 'bg-green-500';
      case 'dusty': return 'bg-yellow-500';
      case 'needs_attention': return 'bg-red-500';
      case 'offline': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'clean': return <CheckCircle className="h-4 w-4" />;
      case 'dusty': return <AlertTriangle className="h-4 w-4" />;
      case 'needs_attention': return <AlertTriangle className="h-4 w-4" />;
      case 'offline': return <Activity className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const calculateSystemStats = () => {
    if (!panelData.length) return { totalPanels: 0, avgEfficiency: 0, totalPower: 0, alertCount: 0 };
    
    const totalPanels = panelData.length;
    const avgEfficiency = panelData.reduce((sum, panel) => sum + panel.efficiency, 0) / totalPanels;
    const totalPower = panelData.reduce((sum, panel) => sum + (panel.efficiency * 10), 0); // Simulated power
    const alertCount = panelData.filter(panel => panel.status === 'needs_attention').length;
    
    return { totalPanels, avgEfficiency, totalPower, alertCount };
  };

  const stats = calculateSystemStats();

  const dustChartData = dustForecast.map(forecast => ({
    time: new Date(forecast.timestamp).toLocaleDateString(),
    dust: forecast.dust_concentration,
    visibility: forecast.visibility,
    wind: forecast.wind_speed
  }));

  const efficiencyChartData = panelData.map(panel => ({
    id: panel.panel_id,
    efficiency: panel.efficiency,
    dust: panel.dust_level
  }));

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const statusDistribution = [
    { name: 'Clean', value: panelData.filter(p => p.status === 'clean').length, color: '#00C49F' },
    { name: 'Dusty', value: panelData.filter(p => p.status === 'dusty').length, color: '#FFBB28' },
    { name: 'Needs Attention', value: panelData.filter(p => p.status === 'needs_attention').length, color: '#FF8042' },
    { name: 'Offline', value: panelData.filter(p => p.status === 'offline').length, color: '#8884D8' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Solar AI Monitoring Dashboard</h1>
            <p className="text-gray-600">Real-time monitoring and intelligent cleaning management</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Last updated: {lastUpdate.toLocaleTimeString()}
            </Badge>
            <Button onClick={fetchDashboardData} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* System Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Panels</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalPanels}</p>
                </div>
                <Sun className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Efficiency</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.avgEfficiency.toFixed(1)}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Power</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalPower.toFixed(0)} kW</p>
                </div>
                <Zap className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Alerts</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.alertCount}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Dashboard Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="panels">Panels</TabsTrigger>
            <TabsTrigger value="cleaning">Cleaning</TabsTrigger>
            <TabsTrigger value="sensors">Sensors</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              
              {/* Dust Forecast Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wind className="h-5 w-5" />
                    Dust Forecast (72 Hours)
                  </CardTitle>
                  <CardDescription>
                    CAMS atmospheric dust concentration forecast
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={dustChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="dust" stroke="#8884d8" name="Dust (μg/m³)" />
                      <Line type="monotone" dataKey="visibility" stroke="#82ca9d" name="Visibility (km)" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Panel Status Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Panel Status Distribution
                  </CardTitle>
                  <CardDescription>
                    Current status of all solar panels
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={statusDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {statusDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

            </div>

            {/* Efficiency vs Dust Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Panel Efficiency vs Dust Level
                </CardTitle>
                <CardDescription>
                  Correlation between dust accumulation and efficiency loss
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={efficiencyChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="id" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="efficiency" fill="#8884d8" name="Efficiency %" />
                    <Bar dataKey="dust" fill="#82ca9d" name="Dust Level %" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Panels Tab */}
          <TabsContent value="panels" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {panelData.map((panel) => (
                <Card 
                  key={panel.panel_id} 
                  className={`cursor-pointer transition-all hover:shadow-lg ${
                    selectedPanels.includes(panel.panel_id) ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => {
                    setSelectedPanels(prev => 
                      prev.includes(panel.panel_id) 
                        ? prev.filter(id => id !== panel.panel_id)
                        : [...prev, panel.panel_id]
                    );
                  }}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">{panel.panel_id}</h3>
                      <Badge className={getStatusColor(panel.status)}>
                        {getStatusIcon(panel.status)}
                      </Badge>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Efficiency:</span>
                        <span className="font-medium">{panel.efficiency}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Dust Level:</span>
                        <span className="font-medium">{panel.dust_level}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Battery:</span>
                        <span className="font-medium">{panel.battery_level}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Temperature:</span>
                        <span className="font-medium">{panel.temperature}°C</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            {selectedPanels.length > 0 && (
              <Card>
                <CardContent className="p-4">
                  <p className="text-sm text-gray-600 mb-2">
                    {selectedPanels.length} panel(s) selected
                  </p>
                  <div className="flex gap-2">
                    <Button size="sm">Schedule Cleaning</Button>
                    <Button size="sm" variant="outline">View Details</Button>
                    <Button size="sm" variant="outline">Export Data</Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Cleaning Tab */}
          <TabsContent value="cleaning" className="space-y-6">
            <MethodSelector 
              selectedPanels={selectedPanels} 
              onMethodSelect={(result) => {
                console.log('Cleaning method selected:', result);
                // Handle method selection result
              }}
            />
          </TabsContent>

          {/* Sensors Tab */}
          <TabsContent value="sensors" className="space-y-6">
            <LiveSensorWidgets refreshInterval={5000} />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Advanced analytics coming soon...</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>ROI Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">ROI calculator integration coming soon...</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Recent Notifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {notifications.map((notification, index) => (
                    <div key={index} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                      <Badge variant="outline">{notification.channel}</Badge>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{notification.template_id}</p>
                        <p className="text-xs text-gray-600">
                          To: {notification.recipient_id} • {new Date(notification.sent_at).toLocaleString()}
                        </p>
                      </div>
                      <Badge className={notification.status === 'sent' ? 'bg-green-500' : 'bg-red-500'}>
                        {notification.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

        </Tabs>
      </div>
    </div>
  );
};

export default EnhancedDashboard;
